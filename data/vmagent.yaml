# VMAgent Configuration
# Generated automatically

global:
  scrape_interval: 30s

scrape_configs:
  - job_name: blackbox_http_get_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "http_get_default" ]
    static_configs:
      - targets:
        - "http://offlineroom.yy.com/domain/repair"
        - "https://loki-grafana-s.sysop.yy.com/"
        labels:
           city: "广州市"
           http_method: "GET"
           ip_protocol: "ip4"
           is_advanced: "false"
           isp: "中国电信"
           isp_code: "CTL"
           latitude: "22.930876"
           longitude: "113.404840"
           method: "HTTP_GET"
           module: "http_get_default"
           node_ip: "***********"
           prober: "http"
           province: "广东省"
           zip_code: "511400"
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$1"
        target_label: "http_protocol"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$2"
        target_label: "target"
      - source_labels: ["__address__"]
        regex: "(https?)://(.+?)/(.*)"
        replacement: "$3"
        target_label: "uri"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: blackbox_tcp_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "tcp_default" ]
    static_configs:
      - targets:
        - "*************:80"
        labels:
           city: "广州市"
           ip_protocol: "ip4"
           is_advanced: "false"
           isp: "中国电信"
           isp_code: "CTL"
           latitude: "22.930876"
           longitude: "113.404840"
           method: "TCP"
           module: "tcp_default"
           node_ip: "***********"
           prober: "tcp"
           province: "广东省"
           zip_code: "511400"
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(.*)"
        replacement: "$1"
        target_label: "target"
      - source_labels: ["__address__"]
        regex: "(.+?):(.*)"
        replacement: "$2"
        target_label: "tcp_port"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: blackbox_icmp_default
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [ "icmp_default" ]
    static_configs:
       - targets:
         - "*************"
         labels:
            city: "广州市"
            ip_protocol: "ip4"
            is_advanced: "false"
            isp: "中国电信"
            isp_code: "CTL"
            latitude: "22.930876"
            longitude: "113.404840"
            method: "ICMP"
            module: "icmp_default"
            node_ip: "***********"
            prober: "icmp"
            province: "广东省"
            zip_code: "511400"
    relabel_configs:
      - source_labels: ["__address__"]
        target_label: "__param_target"
      - source_labels: ["__address__"]
        regex: "(.*)"
        replacement: "$1"
        target_label: "target"
      - source_labels: ["node_ip"]
        target_label: "instance"
      - target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "www_yy_com_dns_custom"
    scrape_interval: 61s
    metrics_path: /probe
    params:
      module:
        - "www_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "2"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "www_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "www.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "yuyin-cache_baizhanlive_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "yuyin-cache_baizhanlive_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "53"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "yuyin-cache_baizhanlive_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "yuyin-cache.baizhanlive.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "pcyy-resource_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "pcyy-resource_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "54"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "pcyy-resource_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "pcyy-resource.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "yy-voice-task_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "yy-voice-task_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "55"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "yy-voice-task_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "yy-voice-task.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "d_g_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "d_g_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "56"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "d_g_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "d.g.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "z_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "z_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "57"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "z_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "z.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "apipubless_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "apipubless_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "58"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "apipubless_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "apipubless.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "clientad_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "clientad_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "59"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "clientad_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "clientad.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
  - job_name: "do-web_yy_com_dns_custom"
    scrape_interval: 60s
    metrics_path: /probe
    params:
      module:
        - "do-web_yy_com_dns_custom"
    static_configs:
      - targets:
          - "*************"
        labels:
          city: "广州市"
          dns_server: "*************"
          ip_protocol: "ip4"
          is_advanced: "true"
          isp: "中国电信"
          isp_code: "CTL"
          item_id: "60"
          latitude: "22.930876"
          longitude: "113.404840"
          method: "DNS"
          module: "do-web_yy_com_dns_custom"
          node_ip: "***********"
          prober: "dns"
          province: "广东省"
          target: "do-web.yy.com"
          zip_code: "511400"
    relabel_configs:
      -
        source_labels: ["__address__" ]
        target_label: "__param_target"
      -
        source_labels: ["node_ip" ]
        target_label: "instance"
      -
        target_label: "__address__"
        replacement: "127.0.0.1:9115"
modules:
  http_get_default:
    prober: http
    timeout: 5s
    http:
      method: GET
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200]
      no_follow_redirects: false
      dns_server: *************
  http_post_default:
    prober: http
    timeout: 5s
    http:
      method: POST
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200]
      no_follow_redirects: false
      dns_server: *************
      headers:
        Content-Type: application/json
  tcp_default:
    prober: tcp
    timeout: 5s
    tcp:
      preferred_ip_protocol: ip4
  icmp_default:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ip4
  www_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "www.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  yuyin-cache_baizhanlive_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "yuyin-cache.baizhanlive.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  pcyy-resource_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "pcyy-resource.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  yy-voice-task_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "yy-voice-task.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  d_g_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "d.g.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  z_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "z.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  apipubless_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "apipubless.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  clientad_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "clientad.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
  do-web_yy_com_dns_custom:
    prober: dns
    timeout: 5s
    dns:
      query_name: "do-web.yy.com"
      query_type: "A"
      valid_rcodes: [NOERROR]
      validate_answer_rrs:
        fail_if_matches_regexp: [".*127.0.0.1"]
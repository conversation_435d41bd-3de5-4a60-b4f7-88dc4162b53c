{"count": 13, "data": [{"id": 1, "name": "offlineroom.yy.com_HTTP_GET", "target": "offlineroom.yy.com", "method": "HTTP_GET", "owner": "dw_wuyiwen", "item_data": "{\"http_protocol\":\"http\",\"uri\":\"/domain/repair\",\"requestMethod\":\"GET\",\"headers\":{}}", "interval": 602, "is_advanced": 0}, {"id": 2, "name": "www.yy.com_DNS", "target": "www.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 61, "is_advanced": 1}, {"id": 53, "name": "yuyin-cache.baizhanlive.com_DNS", "target": "yuyin-cache.baizhanlive.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 54, "name": "pcyy-resource.yy.com_DNS", "target": "pcyy-resource.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 55, "name": "yy-voice-task.yy.com_DNS", "target": "yy-voice-task.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 56, "name": "d.g.yy.com_DNS", "target": "d.g.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 57, "name": "z.yy.com_DNS", "target": "z.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 58, "name": "apipubless.yy.com_DNS", "target": "apipubless.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 59, "name": "clientad.yy.com_DNS", "target": "clientad.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 60, "name": "do-web.yy.com_DNS", "target": "do-web.yy.com", "method": "DNS", "owner": "dw_wuyiwen", "item_data": "{\"query_type\":\"A\",\"valid_rcodes\":[\"NOERROR\"],\"validate_answer_rrs\":{\"fail_if_matches_regexp\":[\".*127.0.0.1\"]}}", "interval": 60, "is_advanced": 1}, {"id": 102, "name": "loki-grafana-s.sysop.yy.com_HTTP_GET", "target": "loki-grafana-s.sysop.yy.com", "method": "HTTP_GET", "owner": "dw_wuyiwen", "item_data": "{\"requestMethod\":\"GET\",\"uri\":\"/\",\"http_protocol\":\"https\"}", "interval": 180, "is_advanced": 0}, {"id": 104, "name": "*************:80_TCP", "target": "*************:80", "method": "TCP", "owner": "dw_wuyiwen", "item_data": "{\"preferred_ip_protocol\":\"ip4\"}", "interval": 180, "is_advanced": 0}, {"id": 105, "name": "*************_ICMP", "target": "*************", "method": "ICMP", "owner": "dw_wuyiwen", "item_data": "{\"preferred_ip_protocol\":\"ip4\"}", "interval": 180, "is_advanced": 0}]}
version: 2.1
orbs:
  prometheus: prometheus/prometheus@0.17.1
executors:
  # Whenever the Go version is updated here, .promu.yml should also be updated.
  golang:
    docker:
      - image: cimg/go:1.24
jobs:
  test:
    executor: golang
    steps:
      - prometheus/setup_environment
      - run: make
      - prometheus/store_artifact:
          file: blackbox_exporter
      - run: git diff --exit-code
  # IPv6 tests require the machine executor.
  # See https://circleci.com/docs/2.0/faq/#can-i-use-ipv6-in-my-tests for details.
  test-ipv6:
    machine: true
    working_directory: /home/<USER>/.go_workspace/src/github.com/prometheus/blackbox_exporter
    # Whenever the Go version is updated here, .promu.yml should also be updated.
    environment:
      DOCKER_TEST_IMAGE_NAME: quay.io/prometheus/golang-builder:1.24-base
    steps:
      - checkout
      - run:
          name: enable ipv6
          command: |
            cat \<<'EOF' | sudo tee /etc/docker/daemon.json
            {
              "ipv6": true,
              "fixed-cidr-v6": "2001:db8:1::/64"
            }
            EOF
            sudo service docker restart
      - run: docker run --rm -t -v "$(pwd):/app" "${DOCKER_TEST_IMAGE_NAME}" -i github.com/prometheus/blackbox_exporter -T
workflows:
  version: 2
  blackbox_exporter:
    jobs:
      - test:
          filters:
            tags:
              only: /.*/
      - test-ipv6:
          filters:
            tags:
              only: /.*/
      - prometheus/build:
          name: build
          filters:
            tags:
              only: /.*/
      - prometheus/publish_master:
          context: org-context
          requires:
            - test
            - test-ipv6
            - build
          filters:
            branches:
              only: master
      - prometheus/publish_release:
          context: org-context
          requires:
            - test
            - test-ipv6
            - build
          filters:
            tags:
              only: /^v[0-9]+(\.[0-9]+){2}(-.+|[^-.]*)$/
            branches:
              ignore: /.*/

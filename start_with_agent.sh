#!/bin/bash

# Blackbox Exporter with Agent Mode Startup Script

# 设置默认参数
BLACKBOX_PORT=${BLACKBOX_PORT:-9115}
AGENT_SERVER=${AGENT_SERVER:-"ws://localhost:8081"}
AGENT_CLIENT_ID=${AGENT_CLIENT_ID:-"default"}
AGENT_CLIENT_IP=${AGENT_CLIENT_IP:-"127.0.0.1"}
AGENT_BIND_IP=${AGENT_BIND_IP:-""}
AGENT_FILE_PATH=${AGENT_FILE_PATH:-"data"}
AGENT_LABELS=${AGENT_LABELS:-""}
SOURCE_IP=${SOURCE_IP:-""}
CONFIG_FILE=${CONFIG_FILE:-"blackbox.yml"}

echo "Starting Blackbox Exporter with Agent Mode"
echo "=========================================="
echo "Blackbox Port: $BLACKBOX_PORT"
echo "Agent Server: $AGENT_SERVER"
echo "Agent Client ID: $AGENT_CLIENT_ID"
echo "Agent Client IP: $AGENT_CLIENT_IP"
echo "Agent File Path: $AGENT_FILE_PATH"
echo "Config File: $CONFIG_FILE"

# 构建启动命令
CMD="./blackbox_exporter"
CMD="$CMD --web.listen-address=:$BLACKBOX_PORT"
CMD="$CMD --config.file=$CONFIG_FILE"

# 添加源IP参数（如果指定）
if [ -n "$SOURCE_IP" ]; then
    CMD="$CMD --source-ip=$SOURCE_IP"
    echo "Source IP: $SOURCE_IP"
fi

# 启用 Agent 模式
CMD="$CMD --agent.enable"
CMD="$CMD --agent.server=$AGENT_SERVER"
CMD="$CMD --agent.client-id=$AGENT_CLIENT_ID"
CMD="$CMD --agent.client-ip=$AGENT_CLIENT_IP"
CMD="$CMD --agent.file-path=$AGENT_FILE_PATH"

# 添加绑定IP参数（如果指定）
if [ -n "$AGENT_BIND_IP" ]; then
    CMD="$CMD --agent.bind-ip=$AGENT_BIND_IP"
    echo "Agent Bind IP: $AGENT_BIND_IP"
fi

# 添加全局标签（如果指定）
if [ -n "$AGENT_LABELS" ]; then
    CMD="$CMD --agent.global-labels=$AGENT_LABELS"
    echo "Agent Labels: $AGENT_LABELS"
fi

echo "=========================================="
echo "Command: $CMD"
echo "=========================================="

# 创建数据目录
mkdir -p "$AGENT_FILE_PATH"

# 启动服务
exec $CMD

package main

import (
	"context"
	"fmt"
	"log"
	"net"

	"github.com/prometheus/blackbox_exporter/config"
	"github.com/prometheus/blackbox_exporter/prober"
	"github.com/prometheus/client_golang/prometheus"
)

func main() {
	fmt.Println("=== ICMP 探测带标签示例 ===")

	// 示例1: 创建一个 ProbeItem（模拟从 items.json 读取的数据）
	item := &prober.ProbeItem{
		ID:       105,
		Name:     "*************_ICMP",
		Target:   "*************",
		Method:   "ICMP",
		Owner:    "dw_wuyiwen",
		ItemData: `{"preferred_ip_protocol":"ip4"}`, // 这个会作为标签添加
		Interval: 180,
		IsAdvanced: 0,
	}

	// 示例2: 创建 ICMP 模块配置
	module := config.Module{
		Prober: "icmp",
		ICMP: config.ICMPProbe{
			IPProtocol:         "ip4",
			IPProtocolFallback: true,
		},
	}

	// 示例3: 创建带 item 信息的 context
	ctx := context.Background()
	ctxWithItem := prober.SetItemInContext(ctx, item)

	// 示例4: 创建注册器
	registry := prometheus.NewRegistry()

	// 示例5: 执行 ICMP 探测（带标签）
	fmt.Println("\n执行带标签的 ICMP 探测...")
	
	// 模拟日志记录器（实际使用中应该使用真实的 logger）
	logger := log.Default()
	
	// 执行探测
	success := prober.ProbeICMP(ctxWithItem, item.Target, module, registry, nil, nil)
	
	if success {
		fmt.Println("✓ ICMP 探测成功")
	} else {
		fmt.Println("✗ ICMP 探测失败")
	}

	// 示例6: 查看生成的指标
	fmt.Println("\n生成的指标标签:")
	labels := prober.GetLabelsFromItem(item)
	for k, v := range labels {
		fmt.Printf("  %s: %s\n", k, v)
	}

	// 示例7: 对比 - 不带标签的探测
	fmt.Println("\n执行不带标签的 ICMP 探测...")
	registry2 := prometheus.NewRegistry()
	success2 := prober.ProbeICMP(context.Background(), item.Target, module, registry2, nil, nil)
	
	if success2 {
		fmt.Println("✓ 传统 ICMP 探测成功")
	} else {
		fmt.Println("✗ 传统 ICMP 探测失败")
	}

	// 示例8: 展示如何在实际应用中使用
	fmt.Println("\n=== 实际应用示例 ===")
	demonstrateRealWorldUsage()
}

func demonstrateRealWorldUsage() {
	// 模拟从 items.json 读取的多个 ICMP 探测项
	items := []*prober.ProbeItem{
		{
			ID:       1,
			Name:     "gateway_ping",
			Target:   "***********",
			Method:   "ICMP",
			Owner:    "network_team",
			ItemData: `{"preferred_ip_protocol":"ip4","description":"Gateway ping check"}`,
			Interval: 30,
			IsAdvanced: 0,
		},
		{
			ID:       2,
			Name:     "dns_server_ping",
			Target:   "*******",
			Method:   "ICMP",
			Owner:    "dns_team",
			ItemData: `{"preferred_ip_protocol":"ip4","description":"Google DNS ping check"}`,
			Interval: 60,
			IsAdvanced: 0,
		},
		{
			ID:       3,
			Name:     "ipv6_ping",
			Target:   "2001:4860:4860::8888",
			Method:   "ICMP",
			Owner:    "ipv6_team",
			ItemData: `{"preferred_ip_protocol":"ip6","description":"IPv6 ping check"}`,
			Interval: 120,
			IsAdvanced: 1,
		},
	}

	// ICMP 模块配置
	icmpModule := config.Module{
		Prober: "icmp",
		ICMP: config.ICMPProbe{
			IPProtocol:         "ip4",
			IPProtocolFallback: true,
		},
	}

	// 对每个探测项执行探测
	for i, item := range items {
		fmt.Printf("\n--- 探测项 %d: %s ---\n", i+1, item.Name)
		
		// 创建带 item 信息的 context
		ctx := prober.SetItemInContext(context.Background(), item)
		
		// 创建独立的注册器
		registry := prometheus.NewRegistry()
		
		// 显示将要使用的标签
		labels := prober.GetLabelsFromItem(item)
		fmt.Printf("标签: %+v\n", labels)
		
		// 执行探测
		success := prober.ProbeICMP(ctx, item.Target, icmpModule, registry, nil, nil)
		
		if success {
			fmt.Printf("结果: ✓ 成功\n")
		} else {
			fmt.Printf("结果: ✗ 失败\n")
		}
	}
}

// 展示如何在 HTTP handler 中集成这个功能
func ExampleHTTPHandler() {
	// 这是一个示例，展示如何在 HTTP handler 中使用带标签的 ICMP 探测
	
	// 假设从请求中获取探测项信息
	item := &prober.ProbeItem{
		ID:       100,
		Name:     "api_server_ping",
		Target:   "api.example.com",
		Method:   "ICMP",
		Owner:    "api_team",
		ItemData: `{"preferred_ip_protocol":"ip4","timeout":"5s"}`,
		Interval: 60,
		IsAdvanced: 0,
	}

	// 创建模块配置
	module := config.Module{
		Prober: "icmp",
		ICMP: config.ICMPProbe{
			IPProtocol:         "ip4",
			IPProtocolFallback: true,
		},
	}

	// 创建带 item 信息的 context
	ctx := prober.SetItemInContext(context.Background(), item)

	// 创建注册器
	registry := prometheus.NewRegistry()

	// 执行探测
	success := prober.ProbeICMP(ctx, item.Target, module, registry, nil, nil)

	// 处理结果
	if success {
		fmt.Printf("探测成功，指标已生成并包含以下标签:\n")
		labels := prober.GetLabelsFromItem(item)
		for k, v := range labels {
			fmt.Printf("  %s: %s\n", k, v)
		}
	} else {
		fmt.Printf("探测失败\n")
	}
}

// 展示如何批量处理多个探测项
func BatchProcessExample() {
	fmt.Println("\n=== 批量处理示例 ===")
	
	// 模拟从数据库或配置文件读取的探测项列表
	items := []*prober.ProbeItem{
		{ID: 1, Name: "server1_ping", Target: "server1.example.com", Method: "ICMP", Owner: "ops", ItemData: `{"env":"prod"}`, Interval: 30},
		{ID: 2, Name: "server2_ping", Target: "server2.example.com", Method: "ICMP", Owner: "ops", ItemData: `{"env":"staging"}`, Interval: 60},
		{ID: 3, Name: "server3_ping", Target: "server3.example.com", Method: "ICMP", Owner: "dev", ItemData: `{"env":"dev"}`, Interval: 120},
	}

	module := config.Module{
		Prober: "icmp",
		ICMP: config.ICMPProbe{
			IPProtocol:         "ip4",
			IPProtocolFallback: true,
		},
	}

	successCount := 0
	totalCount := len(items)

	for _, item := range items {
		ctx := prober.SetItemInContext(context.Background(), item)
		registry := prometheus.NewRegistry()
		
		if prober.ProbeICMP(ctx, item.Target, module, registry, nil, nil) {
			successCount++
			fmt.Printf("✓ %s: 成功\n", item.Name)
		} else {
			fmt.Printf("✗ %s: 失败\n", item.Name)
		}
	}

	fmt.Printf("\n批量探测完成: %d/%d 成功\n", successCount, totalCount)
}

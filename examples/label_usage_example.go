package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"strings"

	"github.com/prometheus/blackbox_exporter/config"
	"github.com/prometheus/blackbox_exporter/prober"
	"github.com/prometheus/client_golang/prometheus"
)

// 这个文件展示了如何使用 prober/utils.go 中的标签管理系统

func main() {
	// 创建上下文和请求
	ctx := context.Background()
	
	// 模拟一个探测请求
	req := &prober.LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
		Module:     config.Module{}, // 实际使用时应该填入真实的模块配置
		SrcIP:      net.ParseIP("***********"),
		Registry:   prometheus.NewRegistry(),
		Context:    make(map[string]interface{}),
	}

	// 示例1: 使用全局标签管理器
	fmt.Println("=== 示例1: 使用全局标签管理器 ===")
	
	// 添加自定义标签提供者
	prober.AddGlobalLabelProvider(prober.NewStaticLabelProvider("environment", map[string]string{
		"environment": "production",
		"region":      "us-west-2",
	}, 70))
	
	// 添加条件标签提供者
	prober.AddGlobalLabelProvider(prober.NewConditionalLabelProvider("ssl", 
		func(ctx context.Context, req *prober.LabelRequest) bool {
			return strings.HasPrefix(req.Target, "https://") || strings.Contains(req.Target, ":443")
		}, 
		map[string]string{"ssl": "true"}, 60))
	
	// 获取标签
	labels, err := prober.GetGlobalLabels(ctx, req)
	if err != nil {
		log.Fatalf("Failed to get labels: %v", err)
	}
	
	fmt.Printf("获取到的标签: %+v\n", labels)

	// 示例2: 快速创建带标签的指标
	fmt.Println("\n=== 示例2: 快速创建带标签的指标 ===")
	
	gauge, labelValues, err := prober.QuickCreateLabeledGauge(ctx, req, "probe_success", "Indicates if probe was successful")
	if err != nil {
		log.Fatalf("Failed to create labeled gauge: %v", err)
	}
	
	fmt.Printf("标签值: %v\n", labelValues)
	
	// 注册并设置指标值
	req.Registry.MustRegister(gauge)
	gauge.WithLabelValues(labelValues...).Set(1.0)
	
	// 示例3: 创建指标集合
	fmt.Println("\n=== 示例3: 创建指标集合 ===")
	
	metricDefs := map[string]string{
		"probe_success":          "Indicates if probe was successful",
		"probe_duration_seconds": "Duration of the probe in seconds",
		"probe_status_code":      "HTTP status code returned by probe",
	}
	
	metrics, labelValues2, err := prober.CreateLabeledMetricSet(ctx, req, metricDefs)
	if err != nil {
		log.Fatalf("Failed to create metric set: %v", err)
	}
	
	fmt.Printf("创建了 %d 个指标，标签值: %v\n", len(metrics), labelValues2)
	
	// 注册并设置指标值
	for name, metric := range metrics {
		req.Registry.MustRegister(metric)
		switch name {
		case "probe_success":
			metric.WithLabelValues(labelValues2...).Set(1.0)
		case "probe_duration_seconds":
			metric.WithLabelValues(labelValues2...).Set(0.123)
		case "probe_status_code":
			metric.WithLabelValues(labelValues2...).Set(200)
		}
	}

	// 示例4: 自定义标签管理器
	fmt.Println("\n=== 示例4: 自定义标签管理器 ===")
	
	customLM := prober.NewLabelManager()
	customLM.SetConflictResolution(prober.Concatenate) // 设置冲突解决策略为连接
	
	// 添加多个提供相同标签的提供者来演示冲突解决
	customLM.AddProvider(prober.NewStaticLabelProvider("provider1", map[string]string{
		"source": "provider1",
	}, 100))
	
	customLM.AddProvider(prober.NewStaticLabelProvider("provider2", map[string]string{
		"source": "provider2",
	}, 90))
	
	customLabels, err := customLM.GetLabels(ctx, req)
	if err != nil {
		log.Fatalf("Failed to get custom labels: %v", err)
	}
	
	fmt.Printf("自定义标签管理器结果 (连接策略): %+v\n", customLabels)

	// 示例5: 动态标签提供者
	fmt.Println("\n=== 示例5: 动态标签提供者 ===")
	
	dynamicProvider := prober.NewDynamicLabelProvider("dynamic", func(ctx context.Context, req *prober.LabelRequest) (map[string]string, error) {
		labels := make(map[string]string)
		
		// 根据目标动态生成标签
		if strings.Contains(req.Target, "example.com") {
			labels["domain_type"] = "example"
		}
		
		if strings.Contains(req.Target, "google.com") {
			labels["domain_type"] = "search_engine"
		}
		
		// 添加目标长度标签
		labels["target_length"] = fmt.Sprintf("%d", len(req.Target))
		
		return labels, nil
	}, 50)
	
	prober.AddGlobalLabelProvider(dynamicProvider)
	
	dynamicLabels, err := prober.GetGlobalLabels(ctx, req)
	if err != nil {
		log.Fatalf("Failed to get dynamic labels: %v", err)
	}
	
	fmt.Printf("包含动态标签的结果: %+v\n", dynamicLabels)

	// 示例6: 增强现有指标
	fmt.Println("\n=== 示例6: 增强现有指标 ===")
	
	// 假设你有一个现有的指标定义
	originalOpts := prometheus.GaugeOpts{
		Name: "existing_metric",
		Help: "An existing metric that needs labels",
	}
	
	enhancedGauge, enhancedLabelValues, err := prober.EnhanceGaugeWithLabels(ctx, req, originalOpts)
	if err != nil {
		log.Fatalf("Failed to enhance gauge: %v", err)
	}
	
	fmt.Printf("增强后的指标标签值: %v\n", enhancedLabelValues)
	
	// 可以注册并使用增强后的指标
	// req.Registry.MustRegister(enhancedGauge)
	// enhancedGauge.WithLabelValues(enhancedLabelValues...).Set(42.0)

	fmt.Println("\n=== 所有示例完成 ===")
}

// 辅助函数：展示如何在实际的探测器中使用标签系统
func ExampleProbeWithLabels(ctx context.Context, target string, module config.Module, registry *prometheus.Registry) bool {
	// 创建标签请求
	req := &prober.LabelRequest{
		Target:     target,
		ModuleName: "http_2xx", // 或其他模块名
		Module:     module,
		Registry:   registry,
		Context:    make(map[string]interface{}),
	}
	
	// 创建带标签的指标
	successGauge, labelValues, err := prober.QuickCreateLabeledGauge(ctx, req, "probe_success", "Probe success indicator")
	if err != nil {
		log.Printf("Failed to create labeled gauge: %v", err)
		return false
	}
	
	durationGauge, _, err := prober.QuickCreateLabeledGauge(ctx, req, "probe_duration_seconds", "Probe duration")
	if err != nil {
		log.Printf("Failed to create duration gauge: %v", err)
		return false
	}
	
	// 注册指标
	registry.MustRegister(successGauge)
	registry.MustRegister(durationGauge)
	
	// 执行实际的探测逻辑...
	success := true // 这里应该是实际的探测结果
	duration := 0.123 // 这里应该是实际的探测时间
	
	// 设置指标值
	if success {
		successGauge.WithLabelValues(labelValues...).Set(1)
	} else {
		successGauge.WithLabelValues(labelValues...).Set(0)
	}
	durationGauge.WithLabelValues(labelValues...).Set(duration)
	
	return success
}

package main

import (
	"fmt"
	"log"

	"github.com/prometheus/blackbox_exporter/prober"
	"github.com/prometheus/client_golang/prometheus"
)

func main() {
	fmt.Println("=== 简单标签管理系统使用示例 ===")

	// 示例1: 基本使用 - 从 items.json 数据创建指标
	fmt.Println("\n1. 基本使用示例:")
	
	// 模拟从 items.json 读取的数据
	item := &prober.ProbeItem{
		ID:         1,
		Name:       "example.com_HTTP_GET",
		Target:     "example.com",
		Method:     "HTTP_GET",
		Owner:      "admin",
		Interval:   60,
		IsAdvanced: 0,
	}

	// 创建单个指标
	successGauge, successLabels := prober.CreateProbeSuccessMetric(item)
	fmt.Printf("成功指标标签: %+v\n", successLabels)

	// 创建注册器并注册指标
	registry := prometheus.NewRegistry()
	registry.MustRegister(successGauge)

	// 设置指标值
	successGauge.With(successLabels).Set(1.0)

	// 示例2: 创建标准指标集合
	fmt.Println("\n2. 标准指标集合示例:")
	
	metrics, labels := prober.CreateStandardProbeMetrics(item)
	fmt.Printf("标准指标标签: %+v\n", labels)
	fmt.Printf("创建的指标数量: %d\n", len(metrics))
	
	for name := range metrics {
		fmt.Printf("- %s\n", name)
	}

	// 示例3: 添加自定义标签
	fmt.Println("\n3. 自定义标签示例:")
	
	customLabels := map[string]string{
		"environment": "production",
		"region":      "us-west-1",
		"team":        "sre",
	}
	
	allLabels := prober.AddCustomLabelToItem(item, customLabels)
	fmt.Printf("包含自定义标签: %+v\n", allLabels)

	// 示例4: 条件标签
	fmt.Println("\n4. 条件标签示例:")
	
	// 为包含 "example.com" 的目标添加特殊标签
	conditionalLabels := prober.GetItemLabelsByCondition(item,
		func(item *prober.ProbeItem) bool {
			return item.Target == "example.com"
		},
		map[string]string{
			"domain_type": "example",
			"priority":    "high",
		})
	
	fmt.Printf("条件标签结果: %+v\n", conditionalLabels)

	// 示例5: 不同类型的探测项
	fmt.Println("\n5. 不同探测类型示例:")
	
	items := []*prober.ProbeItem{
		{ID: 1, Name: "web_http", Target: "example.com", Method: "HTTP_GET", Owner: "web-team", Interval: 30, IsAdvanced: 0},
		{ID: 2, Name: "dns_check", Target: "example.com", Method: "DNS", Owner: "dns-team", Interval: 60, IsAdvanced: 1},
		{ID: 3, Name: "tcp_check", Target: "example.com:80", Method: "TCP", Owner: "net-team", Interval: 120, IsAdvanced: 0},
		{ID: 4, Name: "ping_check", Target: "example.com", Method: "ICMP", Owner: "net-team", Interval: 600, IsAdvanced: 0},
	}

	for _, item := range items {
		labels := prober.GetLabelsFromItem(item)
		fmt.Printf("%s (%s): %+v\n", item.Name, item.Method, labels)
	}

	// 示例6: 在实际探测中使用
	fmt.Println("\n6. 实际探测使用示例:")
	
	// 模拟探测函数
	simulateProbe := func(item *prober.ProbeItem) {
		// 创建指标
		metrics, labels := prober.CreateStandardProbeMetrics(item)
		
		// 注册指标
		registry := prometheus.NewRegistry()
		for _, metric := range metrics {
			registry.MustRegister(metric)
		}
		
		// 模拟探测结果
		success := true
		duration := 0.123
		
		// 设置指标值
		if successMetric, exists := metrics["probe_success"]; exists {
			if success {
				successMetric.With(labels).Set(1)
			} else {
				successMetric.With(labels).Set(0)
			}
		}
		
		if durationMetric, exists := metrics["probe_duration_seconds"]; exists {
			durationMetric.With(labels).Set(duration)
		}
		
		// 根据探测类型设置特定指标
		switch item.Method {
		case "HTTP_GET":
			if statusMetric, exists := metrics["probe_http_status_code"]; exists {
				statusMetric.With(labels).Set(200)
			}
			if lengthMetric, exists := metrics["probe_http_content_length"]; exists {
				lengthMetric.With(labels).Set(1024)
			}
		case "DNS":
			if lookupMetric, exists := metrics["probe_dns_lookup_time_seconds"]; exists {
				lookupMetric.With(labels).Set(0.05)
			}
			if answerMetric, exists := metrics["probe_dns_answer_rrs"]; exists {
				answerMetric.With(labels).Set(1)
			}
		}
		
		fmt.Printf("探测完成: %s, 成功: %v, 耗时: %.3fs\n", item.Name, success, duration)
	}
	
	// 对所有探测项执行探测
	for _, item := range items {
		simulateProbe(item)
	}

	fmt.Println("\n=== 示例完成 ===")
}

// 展示如何在现有探测器代码中集成标签功能
func IntegrateWithExistingProber() {
	// 假设这是现有的探测器函数
	existingProbeFunction := func(target, module string) bool {
		// 原有的探测逻辑...
		return true
	}
	
	// 集成标签功能的新版本
	enhancedProbeFunction := func(item *prober.ProbeItem) bool {
		// 创建带标签的指标
		successGauge, labels := prober.CreateProbeSuccessMetric(item)
		durationGauge, _ := prober.CreateProbeDurationMetric(item)
		
		// 注册指标
		registry := prometheus.NewRegistry()
		registry.MustRegister(successGauge)
		registry.MustRegister(durationGauge)
		
		// 执行原有的探测逻辑
		success := existingProbeFunction(item.Target, item.Method)
		
		// 设置指标值
		if success {
			successGauge.With(labels).Set(1)
		} else {
			successGauge.With(labels).Set(0)
		}
		
		return success
	}
	
	// 使用增强版本
	item := &prober.ProbeItem{
		ID:     1,
		Name:   "test",
		Target: "example.com",
		Method: "HTTP_GET",
		Owner:  "admin",
	}
	
	result := enhancedProbeFunction(item)
	if result {
		log.Println("探测成功，指标已更新")
	}
}

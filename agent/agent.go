package agent

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log/slog"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// Agent 配置结构
type Config struct {
	ServerURL    string
	ClientID     string
	ClientIP     string
	Version      string
	BindIP       string
	BlackboxURL  string
	GlobalLabels string
	FilePath     string
	Logger       *slog.Logger
}

// Agent 主结构
type Agent struct {
	config   *Config
	wsClient *WSClient
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
	logger   *slog.Logger
}

// 创建新的 Agent 实例
func New(config *Config) *Agent {
	ctx, cancel := context.WithCancel(context.Background())

	logger := config.Logger
	if logger == nil {
		logger = slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}

	return &Agent{
		config: config,
		ctx:    ctx,
		cancel: cancel,
		logger: logger,
	}
}

// 启动 Agent
func (a *Agent) Start() error {
	a.logger.Info("Starting agent", "server", a.config.ServerURL, "client_id", a.config.ClientID)

	// 先进行节点注册
	if err := a.autoRegister(); err != nil {
		a.logger.Warn("Node registration failed", "error", err)
		// 注册失败不阻断程序运行，继续执行
	}

	// 从文件获取节点信息并更新配置
	a.updateConfigFromNodeInfo()

	// 定义重连回调函数
	onReconnect := func() {
		if err := a.autoRegister(); err != nil {
			a.logger.Warn("Node registration failed after reconnect", "error", err)
		}
	}

	// 启动WebSocket客户端
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		blackboxHost := getServerHost(a.config.BlackboxURL)
		a.startWSClient(blackboxHost, onReconnect)
	}()

	return nil
}

// 停止 Agent
func (a *Agent) Stop() {
	a.logger.Info("Stopping agent")
	a.cancel()

	// 确保 WebSocket 客户端正确关闭
	if a.wsClient != nil {
		close(a.wsClient.done)
	}

	a.wg.Wait()
	a.logger.Info("Agent stopped")
}

// 等待 Agent 运行
func (a *Agent) Wait() {
	a.wg.Wait()
}

type Items struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Target     string `json:"target"`
	Method     string `json:"method"`
	Owner      string `json:"owner"`
	ItemData   string `json:"item_data"`
	Interval   int    `json:"interval"`
	IsAdvanced int    `json:"is_advanced"`
}

type HTTPItemData struct {
	RequestMethod string `json:"requestMethod"`
	URI           string `json:"uri"`
}

type ItemsResponse struct {
	Count int     `json:"count"`
	Data  []Items `json:"data"`
}

type NodeInfo struct {
	ID         string `json:"id"`
	IPAddr     string `json:"ip_addr"`
	City       string `json:"city"`
	Continent  string `json:"continent"`
	DNS        string `json:"dns_addr"`
	ISP        string `json:"isp"`
	ISPCode    string `json:"isp_code"`
	Province   string `json:"province"`
	ZipCode    string `json:"zip_code"`
	Lngwgs     string `json:"lngwgs"`
	Latwgs     string `json:"latwgs"`
	IPProtocol string `json:"ip_protocol"`
}

// 注册响应结构
type RegisterResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Blackbox struct {
			ConfigBase64  string `json:"config_base64"`
			ConfigContent string `json:"config_content"`
			Count         int    `json:"count"`
		} `json:"blackbox"`
		Items   ItemsResponse `json:"items"`
		Node    NodeInfo      `json:"node"`
		VMAgent struct {
			ConfigBase64  string `json:"config_base64"`
			ConfigContent string `json:"config_content"`
			Count         int    `json:"count"`
		}
	} `json:"data"`
}

// 启动 WebSocket 客户端
func (a *Agent) startWSClient(blackboxHost string, onReconnect func()) {
	a.wsClient = &WSClient{
		version:      a.config.Version,
		serverURL:    a.config.ServerURL,
		filePath:     a.config.FilePath,
		clientID:     a.config.ClientID,
		clientIP:     a.config.ClientIP,
		bindIP:       a.config.BindIP,
		blackboxURL:  blackboxHost,
		done:         make(chan struct{}),
		onFileUpdate: func() { a.logger.Info("Files updated") },
		onReconnect:  onReconnect,
		logger:       a.logger,
		ctx:          a.ctx,
	}

	a.wsClient.run()
}

// 更新配置从节点信息文件
func (a *Agent) updateConfigFromNodeInfo() {
	nodeInfo := a.getNodeInfoFromFile()

	// 从nodeinfo.json获取默认clientID
	if a.config.ClientID == "default" || a.config.ClientID == "" {
		if nodeInfo.ID != "" {
			a.config.ClientID = nodeInfo.ID
		}
	}

	// 从nodeinfo.json获取默认clientIP
	if a.config.ClientIP == "127.0.0.1" || a.config.ClientIP == "" {
		if nodeInfo.IPAddr != "" {
			a.config.ClientIP = nodeInfo.IPAddr
		}
	}

	a.logger.Info("Updated config from node info",
		"client_id", a.config.ClientID,
		"client_ip", a.config.ClientIP)
}

func (a *Agent) getItemsFromFile() []Items {
	itemsPath := filepath.Join(a.config.FilePath, "items.json")
	data, err := os.ReadFile(itemsPath)
	if err != nil {
		a.logger.Warn("Failed to read items.json", "error", err)
		return nil
	}

	var itemsResp ItemsResponse
	if err := json.Unmarshal(data, &itemsResp); err != nil {
		a.logger.Warn("Failed to parse items.json", "error", err)
		return nil
	}

	return itemsResp.Data
}

func (a *Agent) buildLabelsForMethod(item Items) map[string]string {
	// 基础labels
	nodeInfo := a.getNodeInfoFromFile()
	labels := map[string]string{
		"id":        fmt.Sprintf("%d", item.ID),
		"target":    item.Target,
		"name":      item.Name,
		"owner":     item.Owner,
		"isp":       nodeInfo.ISP,
		"isp_code":  nodeInfo.ISPCode,
		"city":      nodeInfo.City,
		"province":  nodeInfo.Province,
		"zip_code":  nodeInfo.ZipCode,
		"longitude": nodeInfo.Lngwgs,
		"latitude":  nodeInfo.Latwgs,
	}

	// 添加全局labels
	if a.config.GlobalLabels != "" {
		globalLabelMap := parseGlobalLabels(a.config.GlobalLabels)
		for k, v := range globalLabelMap {
			labels[k] = v
		}
	}

	// 根据不同method添加特定labels
	switch strings.ToUpper(item.Method) {
	case "HTTP_GET", "HTTP_POST":
		labels["method"] = "http"
	case "TCP":
		labels["method"] = "tcp"
	case "ICMP":
		labels["method"] = "icmp"
	case "DNS":
		labels["dns_addr"] = nodeInfo.DNS
		labels["method"] = "dns"
	default:
		labels["method"] = "unknown"
	}

	return labels
}

// 解析全局labels字符串
func parseGlobalLabels(labelsStr string) map[string]string {
	labels := make(map[string]string)
	if labelsStr == "" {
		return labels
	}

	pairs := strings.Split(labelsStr, ",")
	for _, pair := range pairs {
		kv := strings.SplitN(strings.TrimSpace(pair), "=", 2)
		if len(kv) == 2 {
			key := strings.TrimSpace(kv[0])
			value := strings.TrimSpace(kv[1])
			if key != "" && value != "" {
				labels[key] = value
			}
		}
	}

	return labels
}

// 自动注册功能
func (a *Agent) autoRegister() error {
	a.logger.Info("Starting node registration")

	// 构造注册请求
	registerURL := fmt.Sprintf("http://%s/api/ip/register", getServerHost(a.config.ServerURL))

	req, err := http.NewRequest("POST", registerURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create registration request: %v", err)
	}

	// 添加IP参数（让服务端使用指定IP而不是连接IP）
	q := req.URL.Query()
	if a.config.ClientIP != "" && a.config.ClientIP != "127.0.0.1" {
		q.Add("ip", a.config.ClientIP)
	}
	req.URL.RawQuery = q.Encode()

	// 创建HTTP客户端，如果指定了绑定IP则使用自定义Transport
	client := &http.Client{Timeout: 10 * time.Second}

	if a.config.BindIP != "" {
		localAddr, err := net.ResolveTCPAddr("tcp", a.config.BindIP+":0")
		if err != nil {
			return fmt.Errorf("failed to resolve bind IP: %v", err)
		}

		transport := &http.Transport{
			Dial: func(network, addr string) (net.Conn, error) {
				d := &net.Dialer{
					LocalAddr: localAddr,
					Timeout:   10 * time.Second,
				}
				return d.Dial(network, addr)
			},
		}
		client.Transport = transport

		a.logger.Info("Using bind IP for registration", "bind_ip", a.config.BindIP)
	}

	// 发送注册请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send registration request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("registration failed with status code: %d", resp.StatusCode)
	}

	var result RegisterResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("failed to parse registration response: %v", err)
	}

	// 确保配置文件目录存在
	if err := os.MkdirAll(a.config.FilePath, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}

	// 保存blackbox.yaml配置
	if err := a.saveBlackboxConfig(result.Data.Blackbox.ConfigBase64); err != nil {
		a.logger.Warn("Failed to save blackbox config", "error", err)
	} else {
		// 保存成功后调用blackbox reload接口
		a.reloadBlackboxConfig()
	}

	// 保存vmagent.yaml配置
	if err := a.saveVMAgentConfig(result.Data.VMAgent.ConfigBase64); err != nil {
		a.logger.Warn("Failed to save blackbox config", "error", err)
	} else {
		// 保存成功后调用blackbox reload接口
		a.reloadVMAgentConfig()
	}

	// 保存items.json
	if err := a.saveItemsConfig(result.Data.Items); err != nil {
		a.logger.Warn("Failed to save items config", "error", err)
	}

	// 保存nodeinfo.json
	if err := a.saveNodeInfo(result.Data.Node); err != nil {
		a.logger.Warn("Failed to save node info", "error", err)
	}

	a.logger.Info("Node registration successful")
	return nil
}

// 保存blackbox配置
func (a *Agent) saveBlackboxConfig(configBase64 string) error {
	// Base64解码
	configData, err := base64.StdEncoding.DecodeString(configBase64)
	if err != nil {
		return fmt.Errorf("failed to decode blackbox config: %v", err)
	}

	// 写入文件
	blackboxPath := filepath.Join(a.config.FilePath, "blackbox.yaml")
	if err := os.WriteFile(blackboxPath, configData, 0644); err != nil {
		return fmt.Errorf("failed to write blackbox.yaml: %v", err)
	}

	a.logger.Info("Blackbox config saved", "path", blackboxPath)
	return nil
}

// 保存items配置
func (a *Agent) saveItemsConfig(items ItemsResponse) error {
	// 序列化为JSON
	jsonData, err := json.MarshalIndent(items, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to serialize items config: %v", err)
	}

	// 写入文件
	itemsPath := filepath.Join(a.config.FilePath, "items.json")
	if err := os.WriteFile(itemsPath, jsonData, 0644); err != nil {
		return fmt.Errorf("failed to write items.json: %v", err)
	}

	a.logger.Info("Items config saved", "path", itemsPath)
	return nil
}

// 保存节点信息到文件
func (a *Agent) saveNodeInfo(nodeInfo NodeInfo) error {
	// 构造完整的JSON结构

	// 序列化为JSON
	jsonData, err := json.MarshalIndent(nodeInfo, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to serialize node info: %v", err)
	}

	// 写入文件
	nodeInfoPath := filepath.Join(a.config.FilePath, "nodeinfo.json")
	if err := os.WriteFile(nodeInfoPath, jsonData, 0644); err != nil {
		return fmt.Errorf("failed to write nodeinfo.json: %v", err)
	}

	a.logger.Info("Node info saved", "path", nodeInfoPath)
	return nil
}

// 保存节点信息到文件
func (a *Agent) saveVMAgentConfig(configBase64 string) error {
	// Base64解码
	configData, err := base64.StdEncoding.DecodeString(configBase64)
	if err != nil {
		return fmt.Errorf("failed to decode blackbox config: %v", err)
	}

	// 写入文件
	VMAgentPath := filepath.Join(a.config.FilePath, "vmagent.yaml")
	if err := os.WriteFile(VMAgentPath, configData, 0644); err != nil {
		return fmt.Errorf("failed to write vmagent.yaml: %v", err)
	}

	a.logger.Info("vmagent config saved", "path", VMAgentPath)
	return nil
}

// 获取服务器主机地址
func getServerHost(serverURL string) string {
	var serverHost string
	// 从WebSocket URL提取HTTP地址
	if strings.HasPrefix(serverURL, "ws://") {
		serverHost = strings.TrimPrefix(serverURL, "ws://")
	} else if strings.HasPrefix(serverURL, "wss://") {
		serverHost = strings.TrimPrefix(serverURL, "wss://")
	}

	if strings.HasPrefix(serverURL, "http://") {
		serverHost = strings.TrimPrefix(serverURL, "http://")
	} else if strings.HasPrefix(serverURL, "https://") {
		serverHost = strings.TrimPrefix(serverURL, "https://")
	}

	// 移除 /ws 路径后缀
	if strings.HasSuffix(serverHost, "/ws") {
		serverHost = strings.TrimSuffix(serverHost, "/ws")
	}
	if strings.HasSuffix(serverHost, "/probe") {
		serverHost = strings.TrimSuffix(serverHost, "/probe")
	}

	return serverHost
}

// 解析item_data字段获取实际的target和module
func (a *Agent) parseItemData(item Items) (target, module string) {
	// 默认使用原有字段
	target = item.Target
	module = item.Name

	// 如果是HTTP类型，尝试解析item_data
	if strings.HasPrefix(strings.ToUpper(item.Method), "HTTP_") && item.ItemData != "" {
		var httpData HTTPItemData
		if err := json.Unmarshal([]byte(item.ItemData), &httpData); err == nil {
			if httpData.URI != "" {
				target = item.Target + httpData.URI
			}
		}
	}

	// 如果是DNS类型，使用nodeinfo.json中的dns字段作为target
	if strings.ToUpper(item.Method) == "DNS" {
		if nodeInfo := a.getNodeInfoFromFile(); nodeInfo.DNS != "" {
			target = nodeInfo.DNS
		}
	}

	return target, module
}

func (a *Agent) getNodeInfoFromFile() NodeInfo {
	nodeInfoPath := filepath.Join(a.config.FilePath, "nodeinfo.json")
	data, err := os.ReadFile(nodeInfoPath)
	if err != nil {
		a.logger.Warn("Failed to read nodeinfo.json", "error", err)
	}

	var nodeInfo NodeInfo
	if err := json.Unmarshal(data, &nodeInfo); err != nil {
		a.logger.Warn("Failed to parse nodeinfo.json", "error", err)
	}

	return nodeInfo
}

// 调用blackbox exporter的reload接口
func (a *Agent) reloadBlackboxConfig() {
	blackboxHost := getServerHost(a.config.BlackboxURL)
	reloadURL := fmt.Sprintf("http://%s/-/reload", blackboxHost)

	resp, err := http.Post(reloadURL, "", nil)
	if err != nil {
		a.logger.Warn("Failed to call blackbox reload API", "error", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		a.logger.Info("Blackbox config reloaded successfully")
	} else {
		a.logger.Warn("Blackbox config reload failed", "status_code", resp.StatusCode)
	}
}

// 调用blackbox exporter的reload接口
func (a *Agent) reloadVMAgentConfig() {
	VMAgentHost := "localhost:8429"
	reloadURL := fmt.Sprintf("http://%s/-/reload", VMAgentHost)

	resp, err := http.Post(reloadURL, "", nil)
	if err != nil {
		a.logger.Warn("Failed to call blackbox reload API", "error", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		a.logger.Info("Vmagent config reloaded successfully")
	} else {
		a.logger.Warn("Vmagent config reload failed", "status_code", resp.StatusCode)
	}
}

package agent

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

type FileMessage struct {
	Type      string            `json:"type"`
	Files     []FileData        `json:"files,omitempty"`
	Auth      *AuthData         `json:"auth,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
	Timestamp int64             `json:"timestamp"`
}

type FileData struct {
	Path    string `json:"path"`
	Content string `json:"content"`
	Hash    string `json:"hash"`
}

type AuthData struct {
	Token    string `json:"token"`
	ClientID string `json:"client_id"`
	ClientIP string `json:"client_ip"`
	Version  string `json:"version"`
}

type WSClient struct {
	conn         *websocket.Conn
	clientID     string
	clientIP     string
	version      string
	serverURL    string
	blackboxURL  string
	filePath     string
	bindIP       string // 添加绑定IP字段
	done         chan struct{}
	onFileUpdate func() // 添加文件更新回调
	onReconnect  func() // 添加重连回调
	logger       *slog.Logger
	ctx          context.Context
}

// 保留旧的函数以保持向后兼容性，但标记为已弃用
// Deprecated: Use Agent.Start() instead
func BoceWsClient(serverURL string, blackboxURL string, filePath string, clientID string, clientIP string, wsVersion string, bindIP string, onFileUpdate func(), onReconnect func()) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	client := &WSClient{
		version:      wsVersion,
		serverURL:    serverURL,
		filePath:     filePath,
		clientID:     clientID,
		clientIP:     clientIP,
		bindIP:       bindIP,
		done:         make(chan struct{}),
		onFileUpdate: onFileUpdate,
		onReconnect:  onReconnect,
		blackboxURL:  blackboxURL,
		logger:       logger,
		ctx:          context.Background(),
	}

	// 处理中断信号
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	// 启动客户端
	go client.run()

	// 等待中断信号
	<-interrupt
	logger.Info("Received interrupt signal, shutting down...")
	close(client.done)

	if client.conn != nil {
		client.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		client.conn.Close()
	}
}
func (c *WSClient) run() {
	for {
		select {
		case <-c.done:
			return
		case <-c.ctx.Done():
			return
		default:
			c.connect()
			// 使用带超时的 select 来实现重连间隔
			select {
			case <-c.ctx.Done():
				return
			case <-c.done:
				return
			case <-time.After(5 * time.Second):
				// 继续重连
			}
		}
	}
}

func (c *WSClient) connect() {
	u, err := url.Parse(c.serverURL)
	if err != nil {
		c.logger.Error("Failed to parse URL", "error", err)
		return
	}

	// 创建自定义dialer
	dialer := websocket.DefaultDialer

	// 如果指定了绑定IP，配置本地地址
	if c.bindIP != "" {
		localAddr, err := net.ResolveTCPAddr("tcp", c.bindIP+":0")
		if err != nil {
			c.logger.Error("Failed to resolve bind IP", "error", err)
			return
		}

		dialer = &websocket.Dialer{
			NetDial: func(network, addr string) (net.Conn, error) {
				d := &net.Dialer{
					LocalAddr: localAddr,
					Timeout:   30 * time.Second,
				}
				return d.Dial(network, addr)
			},
			HandshakeTimeout: 45 * time.Second,
		}

		c.logger.Info("Connecting with bind IP", "bind_ip", c.bindIP, "server", c.serverURL)
	} else {
		c.logger.Info("Connecting to server", "server", c.serverURL)
	}

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		c.logger.Error("Connection failed", "error", err)
		return
	}
	defer conn.Close()

	c.conn = conn

	// WebSocket重连后执行注册
	if c.onReconnect != nil {
		c.logger.Info("WebSocket reconnected, executing node registration")
		c.onReconnect()
	}

	// 发送认证信息
	if err := c.authenticate(); err != nil {
		c.logger.Error("Authentication failed", "error", err)
		return
	}

	// 启动消息处理
	done := make(chan struct{})
	go c.readMessages(done)

	// 心跳
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-done:
			return
		case <-c.done:
			return
		case <-ticker.C:
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				log.Printf("发送心跳失败: %v", err)
				return
			}
		}
	}
}

func (c *WSClient) authenticate() error {
	token := c.generateToken()
	authMsg := FileMessage{
		Type: "auth",
		Auth: &AuthData{
			Token:    token,
			ClientID: c.clientID,
			ClientIP: c.clientIP,
			Version:  c.version,
		},
		Timestamp: time.Now().Unix(),
	}

	data, _ := json.Marshal(authMsg)
	return c.conn.WriteMessage(websocket.TextMessage, data)
}

func (c *WSClient) generateToken() string {
	hash := md5.Sum([]byte(c.clientID + "secret_key"))
	return hex.EncodeToString(hash[:])
}

func (c *WSClient) readMessages(done chan struct{}) {
	defer close(done)

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			log.Printf("读取消息失败: %v", err)
			return
		}

		var msg FileMessage
		if err := json.Unmarshal(message, &msg); err != nil {
			log.Printf("解析消息失败: %v", err)
			continue
		}

		c.handleMessage(msg)
	}
}

func (c *WSClient) handleMessage(msg FileMessage) {
	switch msg.Type {
	case "auth_success":
		log.Printf("认证成功, 版本: %s, 客户端: %s, 客户端IP: %s, 客户端外网IP: %s", c.version, c.clientID, c.conn.LocalAddr().String(), c.clientIP)
	case "auth_failed":
		log.Printf("认证失败")
	case "file_push":
		log.Printf("收到文件推送，文件数量: %d", len(msg.Files))
		c.handleFilePush(msg.Files, msg.Metadata)
	}
}

func (c *WSClient) handleFilePush(files []FileData, metadata map[string]string) {
	hasConfigUpdate := false
	hasBlackboxUpdate := false
	hasVMAgentUpdate := false

	for _, file := range files {
		if err := c.saveFile(file); err != nil {
			log.Printf("保存文件失败 %s: %v", file.Path, err)
		} else {
			log.Printf("文件保存成功: %s (hash: %s)", file.Path, file.Hash)

			// 检查是否是配置文件
			if file.Path == c.filePath+"/items.json" || file.Path == c.filePath+"/nodeinfo.json" {
				hasConfigUpdate = true
			}

			// 检查是否是blackbox配置文件
			if file.Path == c.filePath+"/blackbox.yaml" {
				hasBlackboxUpdate = true
			}

			// 检查是否是vmagent配置文件
			if file.Path == c.filePath+"/vmagent.yaml" {
				hasVMAgentUpdate = true
			}
		}
	}

	if metadata != nil {
		log.Printf("元数据: %+v", metadata)
	}

	// 如果有配置文件更新，触发回调
	if hasConfigUpdate && c.onFileUpdate != nil {
		log.Printf("检测到配置文件更新，触发重新加载...")
		c.onFileUpdate()
	}

	// 如果有blackbox配置更新，调用reload接口
	if hasBlackboxUpdate {
		log.Printf("检测到blackbox.yaml更新，调用reload接口...")
		c.reloadBlackboxConfig()
	}

	// 如果vmagent配置更新，调用reload接口
	if hasVMAgentUpdate {
		log.Printf("检测到vmagent.yaml更新，调用reload接口...")
		c.reloadVMAgentConfig()
	}
}

func (c *WSClient) saveFile(file FileData) error {
	// 确保目录存在
	dir := filepath.Dir(file.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 验证文件hash
	hash := md5.Sum([]byte(file.Content))
	expectedHash := hex.EncodeToString(hash[:])
	log.Printf("expectedHash: %s, file.Hash: %s", expectedHash, file.Hash)
	if expectedHash != file.Hash && strings.Contains(file.Path, "vmagent.yaml") == false {
		return fmt.Errorf("文件hash不匹配")
	}

	// 写入文件
	return os.WriteFile(file.Path, []byte(file.Content), 0644)
}

// 调用blackbox exporter的reload接口
func (c *WSClient) reloadBlackboxConfig() {
	reloadURL := fmt.Sprintf("http://%s/-/reload", c.blackboxURL)

	resp, err := http.Post(reloadURL, "", nil)
	if err != nil {
		log.Printf("调用blackbox reload接口失败: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		log.Printf("Blackbox配置重新加载成功")
	} else {
		log.Printf("Blackbox配置重新加载失败，状态码: %d", resp.StatusCode)
	}
}

// 调用blackbox exporter的reload接口
func (c *WSClient) reloadVMAgentConfig() {
	VMAgentHost := "localhost:8429"
	reloadURL := fmt.Sprintf("http://%s/-/reload", VMAgentHost)

	resp, err := http.Post(reloadURL, "", nil)
	if err != nil {
		c.logger.Warn("Failed to call blackbox reload API", "error", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		c.logger.Info("Vmagent config reloaded successfully")
	} else {
		c.logger.Warn("Vmagent config reload failed", "status_code", resp.StatusCode)
	}
}

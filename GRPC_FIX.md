# GRPC Prober 源IP设置修复

## 问题描述
原代码中使用了错误的 gRPC 函数 `grpc.WithContextDialer(dialer.DialContext)`，这个函数不存在或者签名不正确。

## 修复方案
使用 `grpc.WithDialer` 函数来设置自定义的 dialer，这是更兼容的方式。

## 修复前的代码
```go
opts = append(opts, grpc.WithContextDialer(dialer.DialContext))
```

## 修复后的代码
```go
// Set up source IP if provided
if srcIP != nil {
    logger.Info("Using source IP for GRPC requests", "srcIP", srcIP.String())
    dialer := &net.Dialer{
        LocalAddr: &net.TCPAddr{IP: srcIP},
    }
    // Use WithDialer for older gRPC versions
    opts = append(opts, grpc.WithDialer(func(addr string, timeout time.Duration) (net.Conn, error) {
        return dialer.Dial("tcp", addr)
    }))
}
```

## 修复说明
1. **使用正确的函数**: `grpc.WithDialer` 而不是 `grpc.WithContextDialer`
2. **正确的函数签名**: `func(addr string, timeout time.Duration) (net.Conn, error)`
3. **简化实现**: 直接使用 `dialer.Dial("tcp", addr)` 而不是复杂的 context 处理
4. **兼容性**: 这种方式在不同版本的 gRPC 中都能正常工作

## 功能验证
修复后，GRPC prober 可以正确使用指定的源IP地址进行连接：
- 支持 TLS 和非TLS 连接
- 正确绑定源IP地址
- 兼容现有的 gRPC 健康检查功能

## 使用示例
```bash
# 启动时指定源IP
./blackbox_exporter --source-ip=*************

# 测试GRPC探测
curl "http://localhost:9115/probe?target=grpc-server:443&module=grpc_health"
```

修复完成后，所有的 prober (HTTP、TCP、ICMP、DNS、GRPC) 都能正确支持源IP功能。

package prober

import (
	"context"
	"net"
	"testing"

	"github.com/prometheus/blackbox_exporter/config"
	"github.com/prometheus/client_golang/prometheus"
)

func TestStaticLabelProvider(t *testing.T) {
	provider := NewStaticLabelProvider("test", map[string]string{
		"env":    "test",
		"region": "us-west-1",
	}, 100)

	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
	}

	labels, err := provider.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(labels) != 2 {
		t.Fatalf("Expected 2 labels, got %d", len(labels))
	}

	if labels["env"] != "test" {
		t.<PERSON><PERSON><PERSON>("Expected env=test, got env=%s", labels["env"])
	}

	if labels["region"] != "us-west-1" {
		t.<PERSON>("Expected region=us-west-1, got region=%s", labels["region"])
	}
}

func TestDynamicLabelProvider(t *testing.T) {
	provider := NewDynamicLabelProvider("dynamic", func(ctx context.Context, req *LabelRequest) (map[string]string, error) {
		return map[string]string{
			"target": req.Target,
			"module": req.ModuleName,
		}, nil
	}, 90)

	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
	}

	labels, err := provider.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if labels["target"] != "example.com" {
		t.Errorf("Expected target=example.com, got target=%s", labels["target"])
	}

	if labels["module"] != "http_2xx" {
		t.Errorf("Expected module=http_2xx, got module=%s", labels["module"])
	}
}

func TestConditionalLabelProvider(t *testing.T) {
	provider := NewConditionalLabelProvider("conditional",
		func(ctx context.Context, req *LabelRequest) bool {
			return req.Target == "example.com"
		},
		map[string]string{"matched": "true"}, 80)

	// Test condition matches
	req1 := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
	}

	labels1, err := provider.GetLabels(context.Background(), req1)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(labels1) != 1 || labels1["matched"] != "true" {
		t.Errorf("Expected matched=true, got %v", labels1)
	}

	// Test condition doesn't match
	req2 := &LabelRequest{
		Target:     "other.com",
		ModuleName: "http_2xx",
	}

	labels2, err := provider.GetLabels(context.Background(), req2)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if labels2 != nil {
		t.Errorf("Expected nil labels, got %v", labels2)
	}
}

func TestLabelManager(t *testing.T) {
	lm := NewLabelManager()

	// Add providers with different priorities
	lm.AddProvider(NewStaticLabelProvider("high", map[string]string{
		"priority": "high",
		"common":   "high_value",
	}, 100))

	lm.AddProvider(NewStaticLabelProvider("low", map[string]string{
		"priority": "low",
		"common":   "low_value",
	}, 50))

	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
	}

	labels, err := lm.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// High priority provider should win for "common" label
	if labels["common"] != "high_value" {
		t.Errorf("Expected common=high_value, got common=%s", labels["common"])
	}

	// Both unique labels should be present
	if labels["priority"] != "high" {
		t.Errorf("Expected priority=high, got priority=%s", labels["priority"])
	}
}

func TestLabelManagerConflictResolution(t *testing.T) {
	lm := NewLabelManager()
	lm.SetConflictResolution(Concatenate)

	// Add providers with same label key
	lm.AddProvider(NewStaticLabelProvider("provider1", map[string]string{
		"source": "first",
	}, 100))

	lm.AddProvider(NewStaticLabelProvider("provider2", map[string]string{
		"source": "second",
	}, 90))

	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
	}

	labels, err := lm.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// With Concatenate strategy, values should be joined
	if labels["source"] != "first,second" {
		t.Errorf("Expected source=first,second, got source=%s", labels["source"])
	}
}

func TestBuiltinLabelProviders(t *testing.T) {
	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
		SrcIP:      net.ParseIP("***********"),
	}

	// Test target provider
	targetProvider := NewTargetLabelProvider(100)
	labels, err := targetProvider.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if labels["target"] != "example.com" {
		t.Errorf("Expected target=example.com, got target=%s", labels["target"])
	}

	// Test module provider
	moduleProvider := NewModuleLabelProvider(90)
	labels, err = moduleProvider.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if labels["module"] != "http_2xx" {
		t.Errorf("Expected module=http_2xx, got module=%s", labels["module"])
	}

	// Test environment provider
	envProvider := NewEnvironmentLabelProvider("production", 80)
	labels, err = envProvider.GetLabels(context.Background(), req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if labels["environment"] != "production" {
		t.Errorf("Expected environment=production, got environment=%s", labels["environment"])
	}
}

func TestQuickCreateLabeledGauge(t *testing.T) {
	ctx := context.Background()
	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
		Registry:   prometheus.NewRegistry(),
	}

	gauge, labelValues, err := QuickCreateLabeledGauge(ctx, req, "test_metric", "Test metric")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if gauge == nil {
		t.Fatal("Expected gauge to be created")
	}

	if len(labelValues) == 0 {
		t.Error("Expected some label values")
	}

	// Test that we can register and use the gauge
	req.Registry.MustRegister(gauge)
	gauge.WithLabelValues(labelValues...).Set(1.0)
}

func TestCreateLabeledMetricSet(t *testing.T) {
	ctx := context.Background()
	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
		Registry:   prometheus.NewRegistry(),
	}

	metricDefs := map[string]string{
		"test_success":  "Test success indicator",
		"test_duration": "Test duration",
	}

	metrics, labelValues, err := CreateLabeledMetricSet(ctx, req, metricDefs)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(metrics) != 2 {
		t.Fatalf("Expected 2 metrics, got %d", len(metrics))
	}

	if metrics["test_success"] == nil {
		t.Error("Expected test_success metric to be created")
	}

	if metrics["test_duration"] == nil {
		t.Error("Expected test_duration metric to be created")
	}

	if len(labelValues) == 0 {
		t.Error("Expected some label values")
	}

	// Test that we can register and use the metrics
	for _, metric := range metrics {
		req.Registry.MustRegister(metric)
		metric.WithLabelValues(labelValues...).Set(1.0)
	}
}

func TestGlobalLabelManager(t *testing.T) {
	// Reset global state for test
	GlobalLabelManager = NewLabelManager()

	ctx := context.Background()
	req := &LabelRequest{
		Target:     "example.com",
		ModuleName: "http_2xx",
	}

	// Test that default providers are initialized
	labels, err := GetGlobalLabels(ctx, req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	// Should have at least target and module labels from default providers
	if labels["target"] != "example.com" {
		t.Errorf("Expected target=example.com, got target=%s", labels["target"])
	}

	if labels["module"] != "http_2xx" {
		t.Errorf("Expected module=http_2xx, got module=%s", labels["module"])
	}

	// Test adding global provider
	AddGlobalLabelProvider(NewStaticLabelProvider("test", map[string]string{
		"test_label": "test_value",
	}, 70))

	labels, err = GetGlobalLabels(ctx, req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if labels["test_label"] != "test_value" {
		t.Errorf("Expected test_label=test_value, got test_label=%s", labels["test_label"])
	}

	// Test removing global provider
	removed := RemoveGlobalLabelProvider("test")
	if !removed {
		t.Error("Expected provider to be removed")
	}

	labels, err = GetGlobalLabels(ctx, req)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if _, exists := labels["test_label"]; exists {
		t.Error("Expected test_label to be removed")
	}
}

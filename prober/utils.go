// Copyright 2016 The Prometheus Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package prober

import (
	"context"
	"fmt"
	"hash/fnv"
	"log/slog"
	"net"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var protocolToGauge = map[string]float64{
	"ip4": 4,
	"ip6": 6,
}

// Returns the IP for the IPProtocol and lookup time.
func chooseProtocol(ctx context.Context, IPProtocol string, fallbackIPProtocol bool, target string, registry *prometheus.Registry, logger *slog.Logger, srcIP net.IP) (ip *net.IPAddr, lookupTime float64, err error) {
	var fallbackProtocol string
	probeDNSLookupTimeSeconds := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_dns_lookup_time_seconds",
		Help: "Returns the time taken for probe dns lookup in seconds",
	})

	probeIPProtocolGauge := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_ip_protocol",
		Help: "Specifies whether probe ip protocol is IP4 or IP6",
	})

	probeIPAddrHash := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_ip_addr_hash",
		Help: "Specifies the hash of IP address. It's useful to detect if the IP address changes.",
	})
	registry.MustRegister(probeIPProtocolGauge)
	registry.MustRegister(probeDNSLookupTimeSeconds)
	registry.MustRegister(probeIPAddrHash)

	if IPProtocol == "ip6" || IPProtocol == "" {
		IPProtocol = "ip6"
		fallbackProtocol = "ip4"
	} else {
		IPProtocol = "ip4"
		fallbackProtocol = "ip6"
	}

	logger.Info("Resolving target address", "target", target, "ip_protocol", IPProtocol)
	resolveStart := time.Now()

	defer func() {
		lookupTime = time.Since(resolveStart).Seconds()
		probeDNSLookupTimeSeconds.Add(lookupTime)
	}()

	resolver := &net.Resolver{}

	// Set up custom dialer for DNS resolution if source IP is specified
	if srcIP != nil {
		logger.Info("Using source IP for DNS resolution", "srcIP", srcIP.String())
		dialer := &net.Dialer{
			LocalAddr: &net.UDPAddr{IP: srcIP},
		}
		resolver.Dial = func(ctx context.Context, network, address string) (net.Conn, error) {
			return dialer.DialContext(ctx, network, address)
		}
	}
	if !fallbackIPProtocol {
		ips, err := resolver.LookupIP(ctx, IPProtocol, target)
		if err == nil {
			for _, ip := range ips {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(protocolToGauge[IPProtocol])
				probeIPAddrHash.Set(ipHash(ip))
				return &net.IPAddr{IP: ip}, lookupTime, nil
			}
		}
		logger.Error("Resolution with IP protocol failed", "target", target, "ip_protocol", IPProtocol, "err", err)
		return nil, 0.0, err
	}

	ips, err := resolver.LookupIPAddr(ctx, target)
	if err != nil {
		logger.Error("Resolution with IP protocol failed", "target", target, "err", err)
		return nil, 0.0, err
	}

	// Return the IP in the requested protocol.
	var fallback *net.IPAddr
	for _, ip := range ips {
		switch IPProtocol {
		case "ip4":
			if ip.IP.To4() != nil {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(4)
				probeIPAddrHash.Set(ipHash(ip.IP))
				return &ip, lookupTime, nil
			}

			// ip4 as fallback
			fallback = &ip

		case "ip6":
			if ip.IP.To4() == nil {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(6)
				probeIPAddrHash.Set(ipHash(ip.IP))
				return &ip, lookupTime, nil
			}

			// ip6 as fallback
			fallback = &ip
		}
	}

	// Unable to find ip and no fallback set.
	if fallback == nil || !fallbackIPProtocol {
		return nil, 0.0, fmt.Errorf("unable to find ip; no fallback")
	}

	// Use fallback ip protocol.
	if fallbackProtocol == "ip4" {
		probeIPProtocolGauge.Set(4)
	} else {
		probeIPProtocolGauge.Set(6)
	}
	probeIPAddrHash.Set(ipHash(fallback.IP))
	logger.Info("Resolved target address", "target", target, "ip", fallback.String())
	return fallback, lookupTime, nil
}

func ipHash(ip net.IP) float64 {
	h := fnv.New32a()
	if ip.To4() != nil {
		h.Write(ip.To4())
	} else {
		h.Write(ip.To16())
	}
	return float64(h.Sum32())
}

// ============================================================================
// 简单的标签管理功能
// ============================================================================

// LabelFunc 标签生成函数类型
type LabelFunc func(target, module string) map[string]string

// MetricLabels 全局标签配置
var MetricLabels = struct {
	// 静态标签
	Static map[string]string
	// 动态标签生成函数
	Dynamic []LabelFunc
}{
	Static:  make(map[string]string),
	Dynamic: make([]LabelFunc, 0),
}

// AddStaticLabel 添加静态标签
func AddStaticLabel(key, value string) {
	MetricLabels.Static[key] = value
}

// AddDynamicLabel 添加动态标签生成函数
func AddDynamicLabel(fn LabelFunc) {
	MetricLabels.Dynamic = append(MetricLabels.Dynamic, fn)
}

// GetAllLabels 获取所有标签（静态+动态）
func GetAllLabels(target, module string) map[string]string {
	labels := make(map[string]string)

	// 添加静态标签
	for k, v := range MetricLabels.Static {
		labels[k] = v
	}

	// 添加动态标签
	for _, fn := range MetricLabels.Dynamic {
		if fn != nil {
			dynamicLabels := fn(target, module)
			for k, v := range dynamicLabels {
				labels[k] = v
			}
		}
	}

	return labels
}

// CreateGaugeWithLabels 创建带标签的Gauge
func CreateGaugeWithLabels(name, help, target, module string) (*prometheus.GaugeVec, prometheus.Labels) {
	labels := GetAllLabels(target, module)

	// 提取标签名
	var labelNames []string
	labelValues := make(prometheus.Labels)

	for k, v := range labels {
		labelNames = append(labelNames, k)
		labelValues[k] = v
	}

	// 创建GaugeVec
	gaugeVec := prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: name,
		Help: help,
	}, labelNames)

	return gaugeVec, labelValues
}

// CreateCounterWithLabels 创建带标签的Counter
func CreateCounterWithLabels(name, help, target, module string) (*prometheus.CounterVec, prometheus.Labels) {
	labels := GetAllLabels(target, module)

	// 提取标签名
	var labelNames []string
	labelValues := make(prometheus.Labels)

	for k, v := range labels {
		labelNames = append(labelNames, k)
		labelValues[k] = v
	}

	// 创建CounterVec
	counterVec := prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: name,
		Help: help,
	}, labelNames)

	return counterVec, labelValues
}

// ============================================================================
// 便捷函数
// ============================================================================

// 内置的常用标签生成函数
var (
	// TargetLabelFunc 添加target标签
	TargetLabelFunc = func(target, module string) map[string]string {
		return map[string]string{"target": target}
	}

	// ModuleLabelFunc 添加module标签
	ModuleLabelFunc = func(target, module string) map[string]string {
		return map[string]string{"module": module}
	}
)

// 初始化默认标签函数
func init() {
	// 默认添加target和module标签
	AddDynamicLabel(TargetLabelFunc)
	AddDynamicLabel(ModuleLabelFunc)
}

// Copyright 2016 The Prometheus Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package prober

import (
	"context"
	"fmt"
	"hash/fnv"
	"log/slog"
	"net"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/blackbox_exporter/config"
	"github.com/prometheus/client_golang/prometheus"
)

var protocolToGauge = map[string]float64{
	"ip4": 4,
	"ip6": 6,
}

// Returns the IP for the IPProtocol and lookup time.
func chooseProtocol(ctx context.Context, IPProtocol string, fallbackIPProtocol bool, target string, registry *prometheus.Registry, logger *slog.Logger, srcIP net.IP) (ip *net.IPAddr, lookupTime float64, err error) {
	var fallbackProtocol string
	probeDNSLookupTimeSeconds := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_dns_lookup_time_seconds",
		Help: "Returns the time taken for probe dns lookup in seconds",
	})

	probeIPProtocolGauge := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_ip_protocol",
		Help: "Specifies whether probe ip protocol is IP4 or IP6",
	})

	probeIPAddrHash := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_ip_addr_hash",
		Help: "Specifies the hash of IP address. It's useful to detect if the IP address changes.",
	})
	registry.MustRegister(probeIPProtocolGauge)
	registry.MustRegister(probeDNSLookupTimeSeconds)
	registry.MustRegister(probeIPAddrHash)

	if IPProtocol == "ip6" || IPProtocol == "" {
		IPProtocol = "ip6"
		fallbackProtocol = "ip4"
	} else {
		IPProtocol = "ip4"
		fallbackProtocol = "ip6"
	}

	logger.Info("Resolving target address", "target", target, "ip_protocol", IPProtocol)
	resolveStart := time.Now()

	defer func() {
		lookupTime = time.Since(resolveStart).Seconds()
		probeDNSLookupTimeSeconds.Add(lookupTime)
	}()

	resolver := &net.Resolver{}

	// Set up custom dialer for DNS resolution if source IP is specified
	if srcIP != nil {
		logger.Info("Using source IP for DNS resolution", "srcIP", srcIP.String())
		dialer := &net.Dialer{
			LocalAddr: &net.UDPAddr{IP: srcIP},
		}
		resolver.Dial = func(ctx context.Context, network, address string) (net.Conn, error) {
			return dialer.DialContext(ctx, network, address)
		}
	}
	if !fallbackIPProtocol {
		ips, err := resolver.LookupIP(ctx, IPProtocol, target)
		if err == nil {
			for _, ip := range ips {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(protocolToGauge[IPProtocol])
				probeIPAddrHash.Set(ipHash(ip))
				return &net.IPAddr{IP: ip}, lookupTime, nil
			}
		}
		logger.Error("Resolution with IP protocol failed", "target", target, "ip_protocol", IPProtocol, "err", err)
		return nil, 0.0, err
	}

	ips, err := resolver.LookupIPAddr(ctx, target)
	if err != nil {
		logger.Error("Resolution with IP protocol failed", "target", target, "err", err)
		return nil, 0.0, err
	}

	// Return the IP in the requested protocol.
	var fallback *net.IPAddr
	for _, ip := range ips {
		switch IPProtocol {
		case "ip4":
			if ip.IP.To4() != nil {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(4)
				probeIPAddrHash.Set(ipHash(ip.IP))
				return &ip, lookupTime, nil
			}

			// ip4 as fallback
			fallback = &ip

		case "ip6":
			if ip.IP.To4() == nil {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(6)
				probeIPAddrHash.Set(ipHash(ip.IP))
				return &ip, lookupTime, nil
			}

			// ip6 as fallback
			fallback = &ip
		}
	}

	// Unable to find ip and no fallback set.
	if fallback == nil || !fallbackIPProtocol {
		return nil, 0.0, fmt.Errorf("unable to find ip; no fallback")
	}

	// Use fallback ip protocol.
	if fallbackProtocol == "ip4" {
		probeIPProtocolGauge.Set(4)
	} else {
		probeIPProtocolGauge.Set(6)
	}
	probeIPAddrHash.Set(ipHash(fallback.IP))
	logger.Info("Resolved target address", "target", target, "ip", fallback.String())
	return fallback, lookupTime, nil
}

func ipHash(ip net.IP) float64 {
	h := fnv.New32a()
	if ip.To4() != nil {
		h.Write(ip.To4())
	} else {
		h.Write(ip.To16())
	}
	return float64(h.Sum32())
}

// ============================================================================
// Label Management System - 抽象的指标标签管理功能
// ============================================================================

// LabelProvider 定义标签提供者接口
type LabelProvider interface {
	// GetLabels 获取标签，返回 map[string]string
	GetLabels(ctx context.Context, req *LabelRequest) (map[string]string, error)
	// Priority 返回优先级，数字越大优先级越高
	Priority() int
	// Name 返回提供者名称
	Name() string
	// Enabled 返回是否启用
	Enabled() bool
}

// LabelRequest 标签请求上下文
type LabelRequest struct {
	Target     string                 // 目标地址
	Module     config.Module          // 模块配置
	ModuleName string                 // 模块名称
	SrcIP      net.IP                 // 源IP
	Registry   *prometheus.Registry   // 指标注册器
	Context    map[string]interface{} // 额外上下文
}

// ConflictResolution 冲突解决策略
type ConflictResolution int

const (
	HighestPriority ConflictResolution = iota // 使用最高优先级
	FirstWins                                 // 第一个获胜
	LastWins                                  // 最后一个获胜
	Concatenate                               // 连接所有值
)

// LabelManager 标签管理器
type LabelManager struct {
	providers          []LabelProvider
	conflictResolution ConflictResolution
	mu                 sync.RWMutex
}

// NewLabelManager 创建新的标签管理器
func NewLabelManager() *LabelManager {
	return &LabelManager{
		providers:          make([]LabelProvider, 0),
		conflictResolution: HighestPriority,
	}
}

// AddProvider 添加标签提供者
func (lm *LabelManager) AddProvider(provider LabelProvider) {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	lm.providers = append(lm.providers, provider)
	// 按优先级排序
	sort.Slice(lm.providers, func(i, j int) bool {
		return lm.providers[i].Priority() > lm.providers[j].Priority()
	})
}

// RemoveProvider 移除标签提供者
func (lm *LabelManager) RemoveProvider(name string) bool {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	for i, provider := range lm.providers {
		if provider.Name() == name {
			lm.providers = append(lm.providers[:i], lm.providers[i+1:]...)
			return true
		}
	}
	return false
}

// SetConflictResolution 设置冲突解决策略
func (lm *LabelManager) SetConflictResolution(resolution ConflictResolution) {
	lm.mu.Lock()
	defer lm.mu.Unlock()
	lm.conflictResolution = resolution
}

// GetLabels 获取合并后的标签
func (lm *LabelManager) GetLabels(ctx context.Context, req *LabelRequest) (map[string]string, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	allLabels := make(map[string][]labelValue)

	// 收集所有提供者的标签
	for _, provider := range lm.providers {
		if !provider.Enabled() {
			continue
		}

		labels, err := provider.GetLabels(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("provider %s failed: %w", provider.Name(), err)
		}

		if labels == nil {
			continue
		}

		for key, value := range labels {
			allLabels[key] = append(allLabels[key], labelValue{
				value:    value,
				priority: provider.Priority(),
				provider: provider.Name(),
			})
		}
	}

	// 解决冲突并合并标签
	return lm.resolveConflicts(allLabels), nil
}

// labelValue 标签值及其元数据
type labelValue struct {
	value    string
	priority int
	provider string
}

// resolveConflicts 解决标签冲突
func (lm *LabelManager) resolveConflicts(allLabels map[string][]labelValue) map[string]string {
	result := make(map[string]string)

	for key, values := range allLabels {
		if len(values) == 1 {
			result[key] = values[0].value
			continue
		}

		switch lm.conflictResolution {
		case HighestPriority:
			result[key] = values[0].value // 已按优先级排序
		case FirstWins:
			result[key] = values[0].value
		case LastWins:
			result[key] = values[len(values)-1].value
		case Concatenate:
			var vals []string
			for _, v := range values {
				vals = append(vals, v.value)
			}
			result[key] = strings.Join(vals, ",")
		}
	}

	return result
}

// ============================================================================
// 内置标签提供者实现
// ============================================================================

// StaticLabelProvider 静态标签提供者
type StaticLabelProvider struct {
	labels   map[string]string
	priority int
	enabled  bool
	name     string
}

// NewStaticLabelProvider 创建静态标签提供者
func NewStaticLabelProvider(name string, labels map[string]string, priority int) *StaticLabelProvider {
	return &StaticLabelProvider{
		labels:   labels,
		priority: priority,
		enabled:  true,
		name:     name,
	}
}

func (p *StaticLabelProvider) GetLabels(ctx context.Context, req *LabelRequest) (map[string]string, error) {
	if !p.enabled {
		return nil, nil
	}

	result := make(map[string]string)
	for k, v := range p.labels {
		result[k] = v
	}
	return result, nil
}

func (p *StaticLabelProvider) Priority() int { return p.priority }
func (p *StaticLabelProvider) Name() string  { return p.name }
func (p *StaticLabelProvider) Enabled() bool { return p.enabled }
func (p *StaticLabelProvider) SetEnabled(enabled bool) { p.enabled = enabled }

// DynamicLabelProvider 动态标签提供者
type DynamicLabelProvider struct {
	labelFunc func(ctx context.Context, req *LabelRequest) (map[string]string, error)
	priority  int
	enabled   bool
	name      string
}

// NewDynamicLabelProvider 创建动态标签提供者
func NewDynamicLabelProvider(name string, labelFunc func(ctx context.Context, req *LabelRequest) (map[string]string, error), priority int) *DynamicLabelProvider {
	return &DynamicLabelProvider{
		labelFunc: labelFunc,
		priority:  priority,
		enabled:   true,
		name:      name,
	}
}

func (p *DynamicLabelProvider) GetLabels(ctx context.Context, req *LabelRequest) (map[string]string, error) {
	if !p.enabled || p.labelFunc == nil {
		return nil, nil
	}
	return p.labelFunc(ctx, req)
}

func (p *DynamicLabelProvider) Priority() int { return p.priority }
func (p *DynamicLabelProvider) Name() string  { return p.name }
func (p *DynamicLabelProvider) Enabled() bool { return p.enabled }
func (p *DynamicLabelProvider) SetEnabled(enabled bool) { p.enabled = enabled }

// ConditionalLabelProvider 条件标签提供者
type ConditionalLabelProvider struct {
	condition func(ctx context.Context, req *LabelRequest) bool
	labels    map[string]string
	priority  int
	enabled   bool
	name      string
}

// NewConditionalLabelProvider 创建条件标签提供者
func NewConditionalLabelProvider(name string, condition func(ctx context.Context, req *LabelRequest) bool, labels map[string]string, priority int) *ConditionalLabelProvider {
	return &ConditionalLabelProvider{
		condition: condition,
		labels:    labels,
		priority:  priority,
		enabled:   true,
		name:      name,
	}
}

func (p *ConditionalLabelProvider) GetLabels(ctx context.Context, req *LabelRequest) (map[string]string, error) {
	if !p.enabled || p.condition == nil {
		return nil, nil
	}

	if p.condition(ctx, req) {
		result := make(map[string]string)
		for k, v := range p.labels {
			result[k] = v
		}
		return result, nil
	}

	return nil, nil
}

func (p *ConditionalLabelProvider) Priority() int { return p.priority }
func (p *ConditionalLabelProvider) Name() string  { return p.name }
func (p *ConditionalLabelProvider) Enabled() bool { return p.enabled }
func (p *ConditionalLabelProvider) SetEnabled(enabled bool) { p.enabled = enabled }

// ============================================================================
// 辅助函数和工厂方法
// ============================================================================

// CreateGaugeWithLabels 创建带标签的Gauge指标
func CreateGaugeWithLabels(lm *LabelManager, ctx context.Context, req *LabelRequest, opts prometheus.GaugeOpts) (*prometheus.GaugeVec, error) {
	labels, err := lm.GetLabels(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get labels: %w", err)
	}

	// 提取标签名称
	var labelNames []string
	for name := range labels {
		labelNames = append(labelNames, name)
	}
	sort.Strings(labelNames) // 确保顺序一致

	// 创建GaugeVec
	gaugeVec := prometheus.NewGaugeVec(opts, labelNames)

	return gaugeVec, nil
}

// CreateCounterWithLabels 创建带标签的Counter指标
func CreateCounterWithLabels(lm *LabelManager, ctx context.Context, req *LabelRequest, opts prometheus.CounterOpts) (*prometheus.CounterVec, error) {
	labels, err := lm.GetLabels(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get labels: %w", err)
	}

	// 提取标签名称
	var labelNames []string
	for name := range labels {
		labelNames = append(labelNames, name)
	}
	sort.Strings(labelNames) // 确保顺序一致

	// 创建CounterVec
	counterVec := prometheus.NewCounterVec(opts, labelNames)

	return counterVec, nil
}

// CreateHistogramWithLabels 创建带标签的Histogram指标
func CreateHistogramWithLabels(lm *LabelManager, ctx context.Context, req *LabelRequest, opts prometheus.HistogramOpts) (*prometheus.HistogramVec, error) {
	labels, err := lm.GetLabels(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get labels: %w", err)
	}

	// 提取标签名称
	var labelNames []string
	for name := range labels {
		labelNames = append(labelNames, name)
	}
	sort.Strings(labelNames) // 确保顺序一致

	// 创建HistogramVec
	histogramVec := prometheus.NewHistogramVec(opts, labelNames)

	return histogramVec, nil
}

// GetLabelValues 获取标签值数组（按标签名称排序）
func GetLabelValues(lm *LabelManager, ctx context.Context, req *LabelRequest) ([]string, error) {
	labels, err := lm.GetLabels(ctx, req)
	if err != nil {
		return nil, err
	}

	// 按标签名称排序
	var names []string
	for name := range labels {
		names = append(names, name)
	}
	sort.Strings(names)

	// 提取对应的值
	var values []string
	for _, name := range names {
		values = append(values, labels[name])
	}

	return values, nil
}

// ============================================================================
// 常用标签提供者工厂方法
// ============================================================================

// NewTargetLabelProvider 创建目标标签提供者
func NewTargetLabelProvider(priority int) LabelProvider {
	return NewDynamicLabelProvider("target", func(ctx context.Context, req *LabelRequest) (map[string]string, error) {
		return map[string]string{
			"target": req.Target,
		}, nil
	}, priority)
}

// NewModuleLabelProvider 创建模块标签提供者
func NewModuleLabelProvider(priority int) LabelProvider {
	return NewDynamicLabelProvider("module", func(ctx context.Context, req *LabelRequest) (map[string]string, error) {
		return map[string]string{
			"module": req.ModuleName,
		}, nil
	}, priority)
}

// NewIPProtocolLabelProvider 创建IP协议标签提供者
func NewIPProtocolLabelProvider(priority int) LabelProvider {
	return NewDynamicLabelProvider("ip_protocol", func(ctx context.Context, req *LabelRequest) (map[string]string, error) {
		labels := make(map[string]string)

		// 根据模块类型确定IP协议
		switch req.ModuleName {
		case "http_2xx", "http_post_2xx":
			if req.Module.HTTP.IPProtocol != "" {
				labels["ip_protocol"] = req.Module.HTTP.IPProtocol
			}
		case "tcp_connect":
			if req.Module.TCP.IPProtocol != "" {
				labels["ip_protocol"] = req.Module.TCP.IPProtocol
			}
		case "icmp":
			if req.Module.ICMP.IPProtocol != "" {
				labels["ip_protocol"] = req.Module.ICMP.IPProtocol
			}
		case "dns":
			if req.Module.DNS.IPProtocol != "" {
				labels["ip_protocol"] = req.Module.DNS.IPProtocol
			}
		}

		return labels, nil
	}, priority)
}

// NewTimestampLabelProvider 创建时间戳标签提供者
func NewTimestampLabelProvider(priority int) LabelProvider {
	return NewDynamicLabelProvider("timestamp", func(ctx context.Context, req *LabelRequest) (map[string]string, error) {
		return map[string]string{
			"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		}, nil
	}, priority)
}

// NewEnvironmentLabelProvider 创建环境标签提供者
func NewEnvironmentLabelProvider(env string, priority int) LabelProvider {
	return NewStaticLabelProvider("environment", map[string]string{
		"environment": env,
	}, priority)
}

// ============================================================================
// 全局标签管理器和便捷方法
// ============================================================================

var (
	// GlobalLabelManager 全局标签管理器实例
	GlobalLabelManager = NewLabelManager()

	// 初始化锁
	initOnce sync.Once
)

// InitDefaultLabelProviders 初始化默认的标签提供者
func InitDefaultLabelProviders() {
	initOnce.Do(func() {
		// 添加基础标签提供者
		GlobalLabelManager.AddProvider(NewTargetLabelProvider(100))
		GlobalLabelManager.AddProvider(NewModuleLabelProvider(90))
		GlobalLabelManager.AddProvider(NewIPProtocolLabelProvider(80))
	})
}

// AddGlobalLabelProvider 添加全局标签提供者
func AddGlobalLabelProvider(provider LabelProvider) {
	GlobalLabelManager.AddProvider(provider)
}

// RemoveGlobalLabelProvider 移除全局标签提供者
func RemoveGlobalLabelProvider(name string) bool {
	return GlobalLabelManager.RemoveProvider(name)
}

// GetGlobalLabels 获取全局标签
func GetGlobalLabels(ctx context.Context, req *LabelRequest) (map[string]string, error) {
	InitDefaultLabelProviders() // 确保默认提供者已初始化
	return GlobalLabelManager.GetLabels(ctx, req)
}

// ============================================================================
// 使用示例和便捷方法
// ============================================================================

// EnhanceGaugeWithLabels 为现有的Gauge添加标签支持
// 使用示例:
//   gauge := prometheus.NewGauge(prometheus.GaugeOpts{Name: "my_metric", Help: "My metric"})
//   enhancedGauge, err := EnhanceGaugeWithLabels(ctx, req, gauge)
//   if err == nil {
//       registry.MustRegister(enhancedGauge)
//       enhancedGauge.WithLabelValues(labelValues...).Set(1.0)
//   }
func EnhanceGaugeWithLabels(ctx context.Context, req *LabelRequest, originalOpts prometheus.GaugeOpts) (*prometheus.GaugeVec, []string, error) {
	labels, err := GetGlobalLabels(ctx, req)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get labels: %w", err)
	}

	// 提取标签名称并排序
	var labelNames []string
	var labelValues []string
	for name := range labels {
		labelNames = append(labelNames, name)
	}
	sort.Strings(labelNames)

	// 提取对应的值
	for _, name := range labelNames {
		labelValues = append(labelValues, labels[name])
	}

	// 创建GaugeVec
	gaugeVec := prometheus.NewGaugeVec(originalOpts, labelNames)

	return gaugeVec, labelValues, nil
}

// QuickCreateLabeledGauge 快速创建带标签的Gauge
// 使用示例:
//   gauge, labelValues, err := QuickCreateLabeledGauge(ctx, req, "my_metric", "My metric help")
//   if err == nil {
//       registry.MustRegister(gauge)
//       gauge.WithLabelValues(labelValues...).Set(1.0)
//   }
func QuickCreateLabeledGauge(ctx context.Context, req *LabelRequest, name, help string) (*prometheus.GaugeVec, []string, error) {
	opts := prometheus.GaugeOpts{
		Name: name,
		Help: help,
	}
	return EnhanceGaugeWithLabels(ctx, req, opts)
}

// CreateLabeledMetricSet 创建一组带相同标签的指标
// 使用示例:
//   metrics, labelValues, err := CreateLabeledMetricSet(ctx, req, map[string]string{
//       "success": "Probe success indicator",
//       "duration": "Probe duration in seconds",
//   })
func CreateLabeledMetricSet(ctx context.Context, req *LabelRequest, metricDefs map[string]string) (map[string]*prometheus.GaugeVec, []string, error) {
	labels, err := GetGlobalLabels(ctx, req)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get labels: %w", err)
	}

	// 提取标签名称并排序
	var labelNames []string
	var labelValues []string
	for name := range labels {
		labelNames = append(labelNames, name)
	}
	sort.Strings(labelNames)

	// 提取对应的值
	for _, name := range labelNames {
		labelValues = append(labelValues, labels[name])
	}

	// 创建指标集合
	metrics := make(map[string]*prometheus.GaugeVec)
	for metricName, help := range metricDefs {
		opts := prometheus.GaugeOpts{
			Name: metricName,
			Help: help,
		}
		metrics[metricName] = prometheus.NewGaugeVec(opts, labelNames)
	}

	return metrics, labelValues, nil
}

// Copyright 2016 The Prometheus Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package prober

import (
	"context"
	"fmt"
	"hash/fnv"
	"log/slog"
	"net"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var protocolToGauge = map[string]float64{
	"ip4": 4,
	"ip6": 6,
}

// Returns the IP for the IPProtocol and lookup time.
func chooseProtocol(ctx context.Context, IPProtocol string, fallbackIPProtocol bool, target string, registry *prometheus.Registry, logger *slog.Logger, srcIP net.IP) (ip *net.IPAddr, lookupTime float64, err error) {
	var fallbackProtocol string
	probeDNSLookupTimeSeconds := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_dns_lookup_time_seconds",
		Help: "Returns the time taken for probe dns lookup in seconds",
	})

	probeIPProtocolGauge := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_ip_protocol",
		Help: "Specifies whether probe ip protocol is IP4 or IP6",
	})

	probeIPAddrHash := prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "probe_ip_addr_hash",
		Help: "Specifies the hash of IP address. It's useful to detect if the IP address changes.",
	})
	registry.MustRegister(probeIPProtocolGauge)
	registry.MustRegister(probeDNSLookupTimeSeconds)
	registry.MustRegister(probeIPAddrHash)

	if IPProtocol == "ip6" || IPProtocol == "" {
		IPProtocol = "ip6"
		fallbackProtocol = "ip4"
	} else {
		IPProtocol = "ip4"
		fallbackProtocol = "ip6"
	}

	logger.Info("Resolving target address", "target", target, "ip_protocol", IPProtocol)
	resolveStart := time.Now()

	defer func() {
		lookupTime = time.Since(resolveStart).Seconds()
		probeDNSLookupTimeSeconds.Add(lookupTime)
	}()

	resolver := &net.Resolver{}

	// Set up custom dialer for DNS resolution if source IP is specified
	if srcIP != nil {
		logger.Info("Using source IP for DNS resolution", "srcIP", srcIP.String())
		dialer := &net.Dialer{
			LocalAddr: &net.UDPAddr{IP: srcIP},
		}
		resolver.Dial = func(ctx context.Context, network, address string) (net.Conn, error) {
			return dialer.DialContext(ctx, network, address)
		}
	}
	if !fallbackIPProtocol {
		ips, err := resolver.LookupIP(ctx, IPProtocol, target)
		if err == nil {
			for _, ip := range ips {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(protocolToGauge[IPProtocol])
				probeIPAddrHash.Set(ipHash(ip))
				return &net.IPAddr{IP: ip}, lookupTime, nil
			}
		}
		logger.Error("Resolution with IP protocol failed", "target", target, "ip_protocol", IPProtocol, "err", err)
		return nil, 0.0, err
	}

	ips, err := resolver.LookupIPAddr(ctx, target)
	if err != nil {
		logger.Error("Resolution with IP protocol failed", "target", target, "err", err)
		return nil, 0.0, err
	}

	// Return the IP in the requested protocol.
	var fallback *net.IPAddr
	for _, ip := range ips {
		switch IPProtocol {
		case "ip4":
			if ip.IP.To4() != nil {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(4)
				probeIPAddrHash.Set(ipHash(ip.IP))
				return &ip, lookupTime, nil
			}

			// ip4 as fallback
			fallback = &ip

		case "ip6":
			if ip.IP.To4() == nil {
				logger.Info("Resolved target address", "target", target, "ip", ip.String())
				probeIPProtocolGauge.Set(6)
				probeIPAddrHash.Set(ipHash(ip.IP))
				return &ip, lookupTime, nil
			}

			// ip6 as fallback
			fallback = &ip
		}
	}

	// Unable to find ip and no fallback set.
	if fallback == nil || !fallbackIPProtocol {
		return nil, 0.0, fmt.Errorf("unable to find ip; no fallback")
	}

	// Use fallback ip protocol.
	if fallbackProtocol == "ip4" {
		probeIPProtocolGauge.Set(4)
	} else {
		probeIPProtocolGauge.Set(6)
	}
	probeIPAddrHash.Set(ipHash(fallback.IP))
	logger.Info("Resolved target address", "target", target, "ip", fallback.String())
	return fallback, lookupTime, nil
}

func ipHash(ip net.IP) float64 {
	h := fnv.New32a()
	if ip.To4() != nil {
		h.Write(ip.To4())
	} else {
		h.Write(ip.To16())
	}
	return float64(h.Sum32())
}

// ============================================================================
// 简单的标签管理功能
// ============================================================================

// LabelFunc 标签生成函数类型
type LabelFunc func(target, module string) map[string]string

// MetricLabels 全局标签配置
var MetricLabels = struct {
	// 静态标签
	Static map[string]string
	// 动态标签生成函数
	Dynamic []LabelFunc
}{
	Static:  make(map[string]string),
	Dynamic: make([]LabelFunc, 0),
}

// GetAllLabels 获取所有标签（静态+动态）
func GetAllLabels(target, module string) map[string]string {
	labels := make(map[string]string)

	// 添加静态标签
	for k, v := range MetricLabels.Static {
		labels[k] = v
	}

	// 添加动态标签
	for _, fn := range MetricLabels.Dynamic {
		if fn != nil {
			dynamicLabels := fn(target, module)
			for k, v := range dynamicLabels {
				labels[k] = v
			}
		}
	}

	return labels
}

// ============================================================================
// 基于 items.json 的标签管理功能
// ============================================================================

// ProbeItem 表示 items.json 中的一个探测项
type ProbeItem struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Target     string `json:"target"`
	Method     string `json:"method"`
	Owner      string `json:"owner"`
	ItemData   string `json:"item_data"`
	Interval   int    `json:"interval"`
	IsAdvanced int    `json:"is_advanced"`
}

// GetLabelsFromItem 从 ProbeItem 生成标签
func GetLabelsFromItem(item *ProbeItem) map[string]string {
	if item == nil {
		return make(map[string]string)
	}

	labels := make(map[string]string)

	// 基础标签
	labels["target"] = item.Target
	labels["method"] = strings.ToLower(item.Method)
	labels["owner"] = item.Owner
	labels["probe_name"] = item.Name
	labels["probe_id"] = fmt.Sprintf("%d", item.ID)

	// 添加 itemData 作为标签（如果存在）
	if item.ItemData != "" {
		labels["item_data"] = item.ItemData
	}

	// 根据方法类型添加特定标签
	switch strings.ToUpper(item.Method) {
	case "HTTP_GET", "HTTP_POST":
		labels["protocol"] = "http"
		labels["probe_type"] = "http"
	case "TCP":
		labels["protocol"] = "tcp"
		labels["probe_type"] = "tcp"
	case "ICMP":
		labels["protocol"] = "icmp"
		labels["probe_type"] = "icmp"
	case "DNS":
		labels["protocol"] = "dns"
		labels["probe_type"] = "dns"
	default:
		labels["protocol"] = "unknown"
		labels["probe_type"] = "unknown"
	}

	// 添加高级/简单标签
	if item.IsAdvanced == 1 {
		labels["probe_mode"] = "advanced"
	} else {
		labels["probe_mode"] = "simple"
	}

	// 添加间隔标签（分类）
	if item.Interval <= 60 {
		labels["interval_category"] = "frequent"
	} else if item.Interval <= 300 {
		labels["interval_category"] = "normal"
	} else {
		labels["interval_category"] = "infrequent"
	}

	return labels
}

// CreateMetricWithItemLabels 使用 ProbeItem 创建带标签的指标
func CreateMetricWithItemLabels(item *ProbeItem, metricName, help string) (*prometheus.GaugeVec, prometheus.Labels) {
	labels := GetLabelsFromItem(item)

	// 合并全局标签
	globalLabels := GetAllLabels(item.Target, strings.ToLower(item.Method))
	for k, v := range globalLabels {
		if _, exists := labels[k]; !exists {
			labels[k] = v
		}
	}

	// 提取标签名和值
	var labelNames []string
	labelValues := make(prometheus.Labels)

	for k, v := range labels {
		labelNames = append(labelNames, k)
		labelValues[k] = v
	}

	// 创建 GaugeVec
	gaugeVec := prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: metricName,
		Help: help,
	}, labelNames)

	return gaugeVec, labelValues
}

// CreateMultipleMetricsWithItemLabels 为一个 ProbeItem 创建多个指标
func CreateMultipleMetricsWithItemLabels(item *ProbeItem, metricDefs map[string]string) (map[string]*prometheus.GaugeVec, prometheus.Labels) {
	labels := GetLabelsFromItem(item)

	// 合并全局标签
	globalLabels := GetAllLabels(item.Target, strings.ToLower(item.Method))
	for k, v := range globalLabels {
		if _, exists := labels[k]; !exists {
			labels[k] = v
		}
	}

	// 提取标签名和值
	var labelNames []string
	labelValues := make(prometheus.Labels)

	for k, v := range labels {
		labelNames = append(labelNames, k)
		labelValues[k] = v
	}

	// 创建多个指标
	metrics := make(map[string]*prometheus.GaugeVec)
	for metricName, help := range metricDefs {
		metrics[metricName] = prometheus.NewGaugeVec(prometheus.GaugeOpts{
			Name: metricName,
			Help: help,
		}, labelNames)
	}

	return metrics, labelValues
}

// CreateICMPMetricsWithLabels 专门为 ICMP 创建带标签的指标
func CreateICMPMetricsWithLabels(item *ProbeItem) (map[string]*prometheus.GaugeVec, prometheus.Labels) {
	labels := GetLabelsFromItem(item)

	// 合并全局标签
	globalLabels := GetAllLabels(item.Target, strings.ToLower(item.Method))
	for k, v := range globalLabels {
		if _, exists := labels[k]; !exists {
			labels[k] = v
		}
	}

	// 提取标签名和值
	var labelNames []string
	labelValues := make(prometheus.Labels)

	for k, v := range labels {
		labelNames = append(labelNames, k)
		labelValues[k] = v
	}

	// ICMP 特定的指标定义
	metricDefs := map[string]string{
		"probe_icmp_duration_seconds": "Duration of icmp request by phase",
		"probe_icmp_reply_hop_limit":  "Replied packet hop limit (TTL for ipv4)",
	}

	// 创建指标
	metrics := make(map[string]*prometheus.GaugeVec)
	for metricName, help := range metricDefs {
		if metricName == "probe_icmp_duration_seconds" {
			// duration 指标需要 phase 标签
			phaseLabels := append(labelNames, "phase")
			metrics[metricName] = prometheus.NewGaugeVec(prometheus.GaugeOpts{
				Name: metricName,
				Help: help,
			}, phaseLabels)
		} else {
			metrics[metricName] = prometheus.NewGaugeVec(prometheus.GaugeOpts{
				Name: metricName,
				Help: help,
			}, labelNames)
		}
	}

	return metrics, labelValues
}

// GetItemFromContext 从 context 中获取 ProbeItem（如果存在）
func GetItemFromContext(ctx context.Context) *ProbeItem {
	if item, ok := ctx.Value("probe_item").(*ProbeItem); ok {
		return item
	}
	return nil
}

// SetItemInContext 将 ProbeItem 设置到 context 中
func SetItemInContext(ctx context.Context, item *ProbeItem) context.Context {
	return context.WithValue(ctx, "probe_item", item)
}

// ============================================================================
// 便捷函数
// ============================================================================

// AddCustomLabelToItem 为 ProbeItem 添加自定义标签
func AddCustomLabelToItem(item *ProbeItem, customLabels map[string]string) map[string]string {
	labels := GetLabelsFromItem(item)

	// 添加自定义标签
	for k, v := range customLabels {
		labels[k] = v
	}

	return labels
}

// GetItemLabelsByCondition 根据条件获取标签
func GetItemLabelsByCondition(item *ProbeItem, condition func(*ProbeItem) bool, conditionalLabels map[string]string) map[string]string {
	labels := GetLabelsFromItem(item)

	// 如果满足条件，添加条件标签
	if condition != nil && condition(item) {
		for k, v := range conditionalLabels {
			labels[k] = v
		}
	}

	return labels
}

// CreateProbeSuccessMetric 创建探测成功指标（常用）
func CreateProbeSuccessMetric(item *ProbeItem) (*prometheus.GaugeVec, prometheus.Labels) {
	return CreateMetricWithItemLabels(item, "probe_success", "Indicates whether probe was successful")
}

// CreateProbeDurationMetric 创建探测时长指标（常用）
func CreateProbeDurationMetric(item *ProbeItem) (*prometheus.GaugeVec, prometheus.Labels) {
	return CreateMetricWithItemLabels(item, "probe_duration_seconds", "Duration of the probe in seconds")
}

// CreateStandardProbeMetrics 创建标准的探测指标集合
func CreateStandardProbeMetrics(item *ProbeItem) (map[string]*prometheus.GaugeVec, prometheus.Labels) {
	metricDefs := map[string]string{
		"probe_success":          "Indicates whether probe was successful",
		"probe_duration_seconds": "Duration of the probe in seconds",
	}

	// 根据探测类型添加特定指标
	switch strings.ToUpper(item.Method) {
	case "HTTP_GET", "HTTP_POST":
		metricDefs["probe_http_status_code"] = "HTTP status code returned"
		metricDefs["probe_http_content_length"] = "Length of HTTP response content"
	case "DNS":
		metricDefs["probe_dns_lookup_time_seconds"] = "DNS lookup time in seconds"
		metricDefs["probe_dns_answer_rrs"] = "Number of DNS answer records"
	case "TCP":
		metricDefs["probe_tcp_connect_time_seconds"] = "TCP connection time in seconds"
	case "ICMP":
		metricDefs["probe_icmp_rtt_seconds"] = "ICMP round trip time in seconds"
	}

	return CreateMultipleMetricsWithItemLabels(item, metricDefs)
}

// ============================================================================
// 使用示例函数
// ============================================================================

// ExampleUsage 展示如何使用标签功能
func ExampleUsage() {
	// 示例 ProbeItem
	item := &ProbeItem{
		ID:         1,
		Name:       "example.com_HTTP_GET",
		Target:     "example.com",
		Method:     "HTTP_GET",
		Owner:      "admin",
		Interval:   60,
		IsAdvanced: 0,
	}

	// 方法1: 创建单个指标
	successGauge, successLabels := CreateProbeSuccessMetric(item)
	_ = successGauge
	_ = successLabels

	// 方法2: 创建标准指标集合
	metrics, labels := CreateStandardProbeMetrics(item)
	_ = metrics
	_ = labels

	// 方法3: 添加自定义标签
	customLabels := map[string]string{
		"environment": "production",
		"region":      "us-west-1",
	}
	allLabels := AddCustomLabelToItem(item, customLabels)
	_ = allLabels

	// 方法4: 条件标签
	conditionalLabels := GetItemLabelsByCondition(item,
		func(item *ProbeItem) bool {
			return strings.Contains(item.Target, "example.com")
		},
		map[string]string{"domain_type": "example"})
	_ = conditionalLabels
}

# 标签管理系统使用指南

这个标签管理系统为 blackbox-exporter 提供了一个抽象、灵活的方式来给 Prometheus 指标添加标签。系统设计为不改动原有代码，所有功能都集成在 `prober/utils.go` 中。

## 核心概念

### 1. LabelProvider 接口
所有标签提供者都实现这个接口：
```go
type LabelProvider interface {
    GetLabels(ctx context.Context, req *LabelRequest) (map[string]string, error)
    Priority() int    // 优先级，数字越大优先级越高
    Name() string     // 提供者名称
    Enabled() bool    // 是否启用
}
```

### 2. LabelManager 管理器
统一管理所有标签提供者，处理标签合并和冲突解决。

### 3. 冲突解决策略
- `HighestPriority`: 使用最高优先级的值（默认）
- `FirstWins`: 第一个值获胜
- `LastWins`: 最后一个值获胜
- `Concatenate`: 连接所有值（用逗号分隔）

## 内置标签提供者

### 1. StaticLabelProvider - 静态标签
```go
provider := NewStaticLabelProvider("environment", map[string]string{
    "environment": "production",
    "region":      "us-west-2",
}, 100) // 优先级 100
```

### 2. DynamicLabelProvider - 动态标签
```go
provider := NewDynamicLabelProvider("dynamic", func(ctx context.Context, req *LabelRequest) (map[string]string, error) {
    return map[string]string{
        "target_length": fmt.Sprintf("%d", len(req.Target)),
    }, nil
}, 90)
```

### 3. ConditionalLabelProvider - 条件标签
```go
provider := NewConditionalLabelProvider("ssl",
    func(ctx context.Context, req *LabelRequest) bool {
        return strings.HasPrefix(req.Target, "https://")
    },
    map[string]string{"ssl": "true"}, 80)
```

### 4. 内置便捷提供者
- `NewTargetLabelProvider(priority)` - 添加 target 标签
- `NewModuleLabelProvider(priority)` - 添加 module 标签
- `NewIPProtocolLabelProvider(priority)` - 添加 ip_protocol 标签
- `NewTimestampLabelProvider(priority)` - 添加 timestamp 标签
- `NewEnvironmentLabelProvider(env, priority)` - 添加 environment 标签

## 使用方式

### 1. 快速开始 - 使用全局管理器
```go
import "github.com/prometheus/blackbox_exporter/prober"

// 添加自定义标签提供者
prober.AddGlobalLabelProvider(prober.NewStaticLabelProvider("custom", map[string]string{
    "environment": "production",
}, 70))

// 创建带标签的指标
ctx := context.Background()
req := &prober.LabelRequest{
    Target:     "example.com",
    ModuleName: "http_2xx",
    Registry:   registry,
}

gauge, labelValues, err := prober.QuickCreateLabeledGauge(ctx, req, "my_metric", "My metric help")
if err == nil {
    registry.MustRegister(gauge)
    gauge.WithLabelValues(labelValues...).Set(1.0)
}
```

### 2. 创建指标集合
```go
metricDefs := map[string]string{
    "probe_success":          "Probe success indicator",
    "probe_duration_seconds": "Probe duration in seconds",
    "probe_status_code":      "HTTP status code",
}

metrics, labelValues, err := prober.CreateLabeledMetricSet(ctx, req, metricDefs)
if err == nil {
    for name, metric := range metrics {
        registry.MustRegister(metric)
        // 设置指标值...
        metric.WithLabelValues(labelValues...).Set(someValue)
    }
}
```

### 3. 增强现有指标
```go
// 假设你有现有的指标定义
originalOpts := prometheus.GaugeOpts{
    Name: "existing_metric",
    Help: "An existing metric",
}

// 增强为带标签的指标
enhancedGauge, labelValues, err := prober.EnhanceGaugeWithLabels(ctx, req, originalOpts)
if err == nil {
    registry.MustRegister(enhancedGauge)
    enhancedGauge.WithLabelValues(labelValues...).Set(42.0)
}
```

### 4. 自定义标签管理器
```go
// 创建自定义管理器
customLM := prober.NewLabelManager()
customLM.SetConflictResolution(prober.Concatenate)

// 添加提供者
customLM.AddProvider(myProvider)

// 获取标签
labels, err := customLM.GetLabels(ctx, req)
```

## 在探测器中集成

### 修改现有探测器代码
原有代码：
```go
probeSuccessGauge := prometheus.NewGauge(prometheus.GaugeOpts{
    Name: "probe_success",
    Help: "Displays whether or not the probe was a success",
})
registry.MustRegister(probeSuccessGauge)
probeSuccessGauge.Set(1)
```

使用标签系统后：
```go
req := &prober.LabelRequest{
    Target:     target,
    ModuleName: moduleName,
    Module:     module,
    Registry:   registry,
}

gauge, labelValues, err := prober.QuickCreateLabeledGauge(ctx, req, "probe_success", "Displays whether or not the probe was a success")
if err == nil {
    registry.MustRegister(gauge)
    gauge.WithLabelValues(labelValues...).Set(1)
}
```

### 批量创建指标
```go
func createProbeMetrics(ctx context.Context, req *prober.LabelRequest) (map[string]*prometheus.GaugeVec, []string, error) {
    metricDefs := map[string]string{
        "probe_success":          "Displays whether or not the probe was a success",
        "probe_duration_seconds": "Returns how long the probe took to complete in seconds",
        "probe_http_status_code": "Response HTTP status code",
        "probe_http_content_length": "Length of http content response",
    }
    
    return prober.CreateLabeledMetricSet(ctx, req, metricDefs)
}
```

## 高级用法

### 1. 自定义标签提供者
```go
type CustomProvider struct {
    // 你的字段
}

func (p *CustomProvider) GetLabels(ctx context.Context, req *prober.LabelRequest) (map[string]string, error) {
    // 你的逻辑
    return map[string]string{
        "custom_label": "custom_value",
    }, nil
}

func (p *CustomProvider) Priority() int { return 100 }
func (p *CustomProvider) Name() string { return "custom" }
func (p *CustomProvider) Enabled() bool { return true }
```

### 2. 条件标签示例
```go
// 根据目标类型添加不同标签
typeProvider := prober.NewConditionalLabelProvider("target_type",
    func(ctx context.Context, req *prober.LabelRequest) bool {
        return strings.Contains(req.Target, "api.")
    },
    map[string]string{"service_type": "api"}, 60)

// 根据模块添加协议标签
protocolProvider := prober.NewDynamicLabelProvider("protocol", func(ctx context.Context, req *prober.LabelRequest) (map[string]string, error) {
    labels := make(map[string]string)
    switch req.ModuleName {
    case "http_2xx", "http_post_2xx":
        labels["protocol"] = "http"
    case "tcp_connect":
        labels["protocol"] = "tcp"
    case "icmp":
        labels["protocol"] = "icmp"
    case "dns":
        labels["protocol"] = "dns"
    }
    return labels, nil
}, 70)
```

## 最佳实践

1. **优先级规划**: 为不同类型的标签提供者设置合理的优先级
   - 系统级标签: 100-90
   - 环境级标签: 89-80  
   - 应用级标签: 79-70
   - 自定义标签: 69-60

2. **性能考虑**: 
   - 避免在标签提供者中执行耗时操作
   - 对于复杂计算，考虑使用缓存

3. **标签命名**: 
   - 使用一致的命名约定
   - 避免使用特殊字符
   - 保持标签名简洁明了

4. **错误处理**: 
   - 标签提供者应该优雅处理错误
   - 不要因为标签获取失败而影响主要功能

## 示例代码

完整的使用示例请参考 `examples/label_usage_example.go` 文件。

## 测试

运行测试：
```bash
go test ./prober -run TestLabel
```

这个标签管理系统提供了强大而灵活的方式来为 Prometheus 指标添加标签，同时保持了与现有代码的兼容性。

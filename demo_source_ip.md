# Blackbox Exporter 源IP功能演示

## 功能概述

我已经为 Blackbox Exporter 实现了指定源IP的功能，让所有 prober 的网络请求都可以使用指定的出口IP地址。

## 实现的功能

### 1. 全局源IP参数
- 添加了 `--source-ip` 命令行参数
- 可以指定所有探测请求使用的源IP地址

### 2. 各个 Prober 的源IP支持

#### HTTP Prober
- 通过自定义 Dialer 设置源IP
- 支持 HTTP/HTTPS 请求
- 兼容重定向场景

#### TCP Prober  
- 通过 net.Dialer.LocalAddr 设置源IP
- 支持 TCP 和 TLS 连接
- 优先级：模块配置 > 全局配置

#### ICMP Prober
- 通过 ICMP socket 绑定源IP
- 支持 IPv4 和 IPv6
- 优先级：模块配置 > 全局配置

#### DNS Prober
- 通过 DNS client 的 Dialer 设置源IP
- 支持 TCP 和 UDP 协议
- 优先级：模块配置 > 全局配置

#### GRPC Prober
- 通过 grpc.WithContextDialer 设置源IP
- 支持 TLS 和非TLS 连接

## 使用方法

### 1. 命令行启动
```bash
# 指定源IP启动 blackbox exporter
./blackbox_exporter --source-ip=************* --config.file=blackbox.yml
```

### 2. 配置文件中的模块级源IP
```yaml
modules:
  http_2xx:
    prober: http
    http:
      preferred_ip_protocol: "ip4"
  
  tcp_connect:
    prober: tcp
    tcp:
      preferred_ip_protocol: "ip4"
      source_ip_address: "*************"  # 模块级源IP，优先级更高
  
  icmp_test:
    prober: icmp
    icmp:
      preferred_ip_protocol: "ip4"
      source_ip_address: "*************"  # 模块级源IP
  
  dns_test:
    prober: dns
    dns:
      query_name: "example.com"
      source_ip_address: "*************"  # 模块级源IP
```

### 3. 优先级规则
1. **模块配置的 source_ip_address** (最高优先级)
2. **全局 --source-ip 参数** (次优先级)  
3. **系统默认路由** (默认行为)

## 测试验证

### 1. HTTP 测试
```bash
curl "http://localhost:9115/probe?target=httpbin.org/ip&module=http_2xx"
```

### 2. TCP 测试  
```bash
curl "http://localhost:9115/probe?target=google.com:80&module=tcp_connect"
```

### 3. ICMP 测试
```bash
curl "http://localhost:9115/probe?target=*******&module=icmp_test"
```

### 4. DNS 测试
```bash
curl "http://localhost:9115/probe?target=*******&module=dns_test"
```

## 日志输出示例

启用源IP后，日志中会显示：
```
level=INFO msg="Using source IP for HTTP requests" srcIP=*************
level=INFO msg="Using global source address" srcIP=*************
level=INFO msg="Using module-configured local address" srcIP=*************
```

## 代码修改总结

1. **修改了 ProbeFn 类型定义**：添加了 srcIP 参数
2. **更新了所有 Prober 函数**：ProbeHTTP, ProbeTCP, ProbeICMP, ProbeDNS, ProbeGRPC
3. **修改了 Handler 函数**：正确传递源IP参数
4. **实现了源IP设置逻辑**：每个 prober 都支持源IP绑定
5. **保持了向后兼容性**：现有配置文件无需修改

## 注意事项

1. **权限要求**：ICMP prober 可能需要 root 权限或 CAP_NET_RAW 能力
2. **网络接口**：确保指定的源IP地址在本机网络接口上存在
3. **防火墙规则**：确保防火墙允许从指定源IP发出的连接
4. **IPv4/IPv6**：源IP地址的协议版本需要与目标地址匹配

这个实现让 Blackbox Exporter 可以在多IP环境中精确控制出口IP地址，满足网络监控的特定需求。
